using System.Collections.ObjectModel;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.IPTT;
using TYFManagementSystem.Services.IPTT;
using TYFManagementSystem.ViewModels;

namespace TYFManagementSystem.ViewModels.IPTT
{
    /// <summary>
    /// ViewModel لإدارة موقع واحد في IPTT
    /// </summary>
    public class IpttLocationViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly IpttCalculationService _calculationService;
        private readonly IpttValidationService _validationService;
        
        private int _locationId;
        private string _locationName;
        private ObservableCollection<IpttDisplayRow> _rows;
        private List<string> _monthColumns;
        private bool _isReadOnly;
        private bool _hasChanges;
        private string _statusMessage = "";
        #endregion

        #region Public Properties
        public int LocationId
        {
            get => _locationId;
            set => SetProperty(ref _locationId, value);
        }

        public string LocationName
        {
            get => _locationName;
            set => SetProperty(ref _locationName, value);
        }

        public ObservableCollection<IpttDisplayRow> Rows
        {
            get => _rows;
            set => SetProperty(ref _rows, value);
        }

        public List<string> MonthColumns
        {
            get => _monthColumns;
            set => SetProperty(ref _monthColumns, value);
        }

        public bool IsReadOnly
        {
            get => _isReadOnly;
            set => SetProperty(ref _isReadOnly, value);
        }

        public bool HasChanges
        {
            get => _hasChanges;
            set => SetProperty(ref _hasChanges, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }
        #endregion

        #region Commands
        public ICommand CalculateCommand { get; private set; }
        public ICommand ValidateCommand { get; private set; }
        public ICommand ClearDataCommand { get; private set; }
        public ICommand CopyFromLocationCommand { get; private set; }
        #endregion

        #region Events
        public event EventHandler<LocationDataChangedEventArgs>? DataChanged;
        public event EventHandler<LocationCalculatedEventArgs>? CalculationCompleted;
        #endregion

        #region Constructor
        public IpttLocationViewModel(
            int locationId, 
            string locationName, 
            ObservableCollection<IpttDisplayRow> rows,
            List<string> monthColumns,
            bool isReadOnly = false)
        {
            _calculationService = new IpttCalculationService();
            _validationService = new IpttValidationService();

            LocationId = locationId;
            LocationName = locationName;
            Rows = rows ?? new ObservableCollection<IpttDisplayRow>();
            MonthColumns = monthColumns ?? new List<string>();
            IsReadOnly = isReadOnly;

            InitializeCommands();
            SetupRowChangeTracking();
        }
        #endregion

        #region Command Initialization
        private void InitializeCommands()
        {
            CalculateCommand = new RelayCommand(Calculate, () => !IsReadOnly);
            ValidateCommand = new RelayCommand(Validate);
            ClearDataCommand = new RelayCommand(ClearData, () => !IsReadOnly);
            CopyFromLocationCommand = new RelayCommand(param => CopyFromLocation(param as IpttLocationViewModel), param => param != null && !IsReadOnly);
        }
        #endregion

        #region Row Change Tracking
        private void SetupRowChangeTracking()
        {
            if (Rows != null)
            {
                foreach (var row in Rows)
                {
                    row.PropertyChanged += Row_PropertyChanged;
                }

                Rows.CollectionChanged += (s, e) =>
                {
                    if (e.NewItems != null)
                    {
                        foreach (IpttDisplayRow row in e.NewItems)
                        {
                            row.PropertyChanged += Row_PropertyChanged;
                        }
                    }

                    if (e.OldItems != null)
                    {
                        foreach (IpttDisplayRow row in e.OldItems)
                        {
                            row.PropertyChanged -= Row_PropertyChanged;
                        }
                    }

                    OnDataChanged();
                };
            }
        }

        private void Row_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (sender is IpttDisplayRow row)
            {
                // تحديث الحسابات تلقائياً عند تغيير البيانات الشهرية
                if (e.PropertyName == nameof(IpttDisplayRow.MonthlyData) || 
                    e.PropertyName == nameof(IpttDisplayRow.Target))
                {
                    CalculateRowAchievement(row);
                    OnDataChanged();
                }
            }
        }
        #endregion

        #region Calculation Methods
        private void Calculate()
        {
            try
            {
                StatusMessage = "جاري حساب الإنجاز...";

                _calculationService.CalculateAchievementForLocation(Rows, MonthColumns);

                var completedRows = Rows.Count(r => r.IsCompleted);
                var totalDataRows = Rows.Count(r => r.IsDataTypeRow && !r.IsTotalRow);
                
                StatusMessage = $"تم الحساب - مكتمل: {completedRows}/{totalDataRows}";

                OnCalculationCompleted();
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في الحساب: {ex.Message}";
            }
        }

        private void CalculateRowAchievement(IpttDisplayRow row)
        {
            try
            {
                if (row.IsDataTypeRow && !row.IsTotalRow)
                {
                    // حساب الإنجاز من مجموع الأشهر
                    double totalAchievement = 0;
                    foreach (var month in MonthColumns)
                    {
                        if (row.MonthlyData.ContainsKey(month) &&
                            double.TryParse(row.MonthlyData[month], out double value))
                        {
                            totalAchievement += value;
                        }
                    }

                    row.Achievement = totalAchievement.ToString("F0");

                    // حساب النسبة المئوية
                    if (double.TryParse(row.Target, out double target) && target > 0)
                    {
                        double percentage = (totalAchievement / target) * 100;
                        row.AchievementPercentage = $"{percentage:F1}%";
                        row.IsCompleted = percentage >= 100;
                    }
                    else
                    {
                        row.AchievementPercentage = "0.0%";
                        row.IsCompleted = false;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب الصف: {ex.Message}");
            }
        }
        #endregion

        #region Validation
        private void Validate()
        {
            try
            {
                var validation = _validationService.ValidateLocationData(Rows, LocationId);
                
                if (validation.IsValid)
                {
                    StatusMessage = "البيانات صحيحة";
                }
                else
                {
                    var errorCount = validation.Errors.Count;
                    var warningCount = validation.Warnings.Count;
                    StatusMessage = $"أخطاء: {errorCount}, تحذيرات: {warningCount}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التحقق: {ex.Message}";
            }
        }
        #endregion

        #region Data Operations
        private void ClearData()
        {
            try
            {
                foreach (var row in Rows.Where(r => r.IsDataTypeRow && !r.IsTotalRow))
                {
                    // مسح البيانات الشهرية
                    foreach (var month in MonthColumns)
                    {
                        if (row.MonthlyData.ContainsKey(month))
                        {
                            row.MonthlyData[month] = "";
                        }
                    }

                    // مسح الحسابات
                    row.Achievement = "";
                    row.AchievementPercentage = "";
                    row.IsCompleted = false;
                }

                StatusMessage = "تم مسح البيانات";
                OnDataChanged();
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في مسح البيانات: {ex.Message}";
            }
        }

        private void CopyFromLocation(IpttLocationViewModel sourceLocation)
        {
            try
            {
                if (sourceLocation == null || sourceLocation.LocationId == LocationId)
                    return;

                for (int i = 0; i < Math.Min(Rows.Count, sourceLocation.Rows.Count); i++)
                {
                    var targetRow = Rows[i];
                    var sourceRow = sourceLocation.Rows[i];

                    // نسخ البيانات الشهرية فقط للصفوف المطابقة
                    if (targetRow.IndicatorId == sourceRow.IndicatorId &&
                        targetRow.DataType == sourceRow.DataType &&
                        targetRow.IsDataTypeRow == sourceRow.IsDataTypeRow)
                    {
                        foreach (var monthData in sourceRow.MonthlyData)
                        {
                            if (targetRow.MonthlyData.ContainsKey(monthData.Key))
                            {
                                targetRow.MonthlyData[monthData.Key] = monthData.Value;
                            }
                        }

                        // نسخ الهدف إذا كان فارغاً
                        if (string.IsNullOrWhiteSpace(targetRow.Target))
                        {
                            targetRow.Target = sourceRow.Target;
                        }
                    }
                }

                // إعادة حساب الإنجاز
                Calculate();
                
                StatusMessage = $"تم نسخ البيانات من {sourceLocation.LocationName}";
                OnDataChanged();
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في نسخ البيانات: {ex.Message}";
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// تحديث البيانات الشهرية لصف معين
        /// </summary>
        public void UpdateMonthlyData(string indicatorId, string dataType, string month, string value)
        {
            try
            {
                var row = Rows.FirstOrDefault(r => 
                    r.IndicatorId == indicatorId && 
                    r.DataType == dataType && 
                    r.IsDataTypeRow);

                if (row != null && row.MonthlyData.ContainsKey(month))
                {
                    row.MonthlyData[month] = value;
                    CalculateRowAchievement(row);
                    OnDataChanged();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحديث البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// تحديث هدف صف معين
        /// </summary>
        public void UpdateTarget(string indicatorId, string dataType, string target)
        {
            try
            {
                var row = Rows.FirstOrDefault(r => 
                    r.IndicatorId == indicatorId && 
                    r.DataType == dataType && 
                    r.IsDataTypeRow);

                if (row != null)
                {
                    row.Target = target;
                    CalculateRowAchievement(row);
                    OnDataChanged();
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحديث الهدف: {ex.Message}";
            }
        }

        /// <summary>
        /// الحصول على ملخص الإنجاز للموقع
        /// </summary>
        public LocationSummary GetSummary()
        {
            var dataTypeRows = Rows.Where(r => r.IsDataTypeRow && !r.IsTotalRow).ToList();
            var completedRows = dataTypeRows.Count(r => r.IsCompleted);
            
            double totalTarget = 0;
            double totalAchievement = 0;

            foreach (var row in dataTypeRows)
            {
                if (double.TryParse(row.Target, out double target))
                    totalTarget += target;
                
                if (double.TryParse(row.Achievement, out double achievement))
                    totalAchievement += achievement;
            }

            return new LocationSummary
            {
                LocationId = LocationId,
                LocationName = LocationName,
                TotalRows = dataTypeRows.Count,
                CompletedRows = completedRows,
                TotalTarget = totalTarget,
                TotalAchievement = totalAchievement,
                CompletionPercentage = totalTarget > 0 ? (totalAchievement / totalTarget) * 100 : 0
            };
        }
        #endregion

        #region Event Handlers
        private void OnDataChanged()
        {
            HasChanges = true;
            DataChanged?.Invoke(this, new LocationDataChangedEventArgs(LocationId, LocationName));
        }

        private void OnCalculationCompleted()
        {
            var summary = GetSummary();
            CalculationCompleted?.Invoke(this, new LocationCalculatedEventArgs(summary));
        }
        #endregion
    }

    #region Event Args Classes
    public class LocationDataChangedEventArgs : EventArgs
    {
        public int LocationId { get; }
        public string LocationName { get; }

        public LocationDataChangedEventArgs(int locationId, string locationName)
        {
            LocationId = locationId;
            LocationName = locationName;
        }
    }

    public class LocationCalculatedEventArgs : EventArgs
    {
        public LocationSummary Summary { get; }

        public LocationCalculatedEventArgs(LocationSummary summary)
        {
            Summary = summary;
        }
    }

    public class LocationSummary
    {
        public int LocationId { get; set; }
        public string LocationName { get; set; } = "";
        public int TotalRows { get; set; }
        public int CompletedRows { get; set; }
        public double TotalTarget { get; set; }
        public double TotalAchievement { get; set; }
        public double CompletionPercentage { get; set; }
    }
    #endregion
}
