using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TYFManagementSystem.Models
{
    public class Project
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string NameArabic { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? DescriptionArabic { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Planning"; // Planning, Active, Completed, Cancelled

        [Column(TypeName = "decimal(18,2)")]
        public decimal Budget { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SpentAmount { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Foreign Keys
        public int CreatedByUserId { get; set; }
        public int? UpdatedByUserId { get; set; }

        // Navigation Properties
        [ForeignKey("CreatedByUserId")]
        public virtual User CreatedByUser { get; set; } = null!;

        [ForeignKey("UpdatedByUserId")]
        public virtual User? UpdatedByUser { get; set; }

        public virtual ICollection<Beneficiary> Beneficiaries { get; set; } = new List<Beneficiary>();

        [NotMapped]
        public decimal RemainingBudget => Budget - SpentAmount;

        [NotMapped]
        public double BudgetUtilizationPercentage => Budget > 0 ? (double)(SpentAmount / Budget) * 100 : 0;
    }
}
