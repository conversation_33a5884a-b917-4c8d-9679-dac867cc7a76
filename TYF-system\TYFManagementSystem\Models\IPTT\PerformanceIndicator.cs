using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TYFManagementSystem.Models.IPTT
{
    // IPTT Indicator data model
    public class IpttIndicator
    {
        public string No { get; set; } = "";
        public string Indicator { get; set; } = "";
        public ObservableCollection<DataTypeItem> DataTypes { get; set; } = new ObservableCollection<DataTypeItem>();
        public string DataType { get; set; } = ""; // For display purposes
        public string Target { get; set; } = "";
        public Dictionary<string, string> MonthlyValues { get; set; } = new Dictionary<string, string>();
    }

    // Data type item for each indicator
    public class DataTypeItem
    {
        public string Name { get; set; } = "";
        public string Target { get; set; } = "";
        public Dictionary<string, string> MonthlyValues { get; set; } = new Dictionary<string, string>();
    }

    // New model for displaying rows in the grid
    public class IpttDisplayRow : INotifyPropertyChanged
    {
        private string _achievement = "";
        private string _achievementPercentage = "";

        public string No { get; set; } = "";
        public string Indicator { get; set; } = "";
        public string DataType { get; set; } = "";
        public string Target { get; set; } = "";

        public string Achievement
        {
            get => _achievement;
            set
            {
                if (_achievement != value)
                {
                    _achievement = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AchievementPercentage
        {
            get => _achievementPercentage;
            set
            {
                if (_achievementPercentage != value)
                {
                    _achievementPercentage = value;
                    OnPropertyChanged();
                }
            }
        }

        public Dictionary<string, string> MonthlyData { get; set; } = new Dictionary<string, string>();
        public bool IsMainIndicator { get; set; } = false;
        public bool IsDataTypeRow { get; set; } = false;
        public bool IsTotalRow { get; set; } = false;
        public bool IsCompleted { get; set; } = false; // للمؤشرات المكتملة 100%
        public bool CanDelete { get; set; } = false; // لإظهار زر الحذف
        public string IndicatorId { get; set; } = ""; // To group rows by indicator

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class PerformanceIndicator : INotifyPropertyChanged
    {
        private string _indicatorNumber = string.Empty;
        private string _name = string.Empty;
        private string _unit = string.Empty;
        private double _targetValue;
        private double _currentValue;
        private double _percentageAchieved;
        private string _status = string.Empty;
        private string _description = string.Empty;
        private DateTime _lastUpdated;
        private string _dataSource = string.Empty;
        private string _responsiblePerson = string.Empty;

        public string IndicatorNumber
        {
            get => _indicatorNumber;
            set => SetProperty(ref _indicatorNumber, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value);
        }

        public double TargetValue
        {
            get => _targetValue;
            set
            {
                SetProperty(ref _targetValue, value);
                UpdatePercentage();
            }
        }

        public double CurrentValue
        {
            get => _currentValue;
            set
            {
                SetProperty(ref _currentValue, value);
                UpdatePercentage();
            }
        }

        public double PercentageAchieved
        {
            get => _percentageAchieved;
            private set => SetProperty(ref _percentageAchieved, value);
        }

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DateTime LastUpdated
        {
            get => _lastUpdated;
            set => SetProperty(ref _lastUpdated, value);
        }

        public string DataSource
        {
            get => _dataSource;
            set => SetProperty(ref _dataSource, value);
        }

        public string ResponsiblePerson
        {
            get => _responsiblePerson;
            set => SetProperty(ref _responsiblePerson, value);
        }

        public PerformanceIndicator()
        {
            LastUpdated = DateTime.Now;
        }

        public void UpdatePercentage()
        {
            if (TargetValue > 0)
            {
                PercentageAchieved = Math.Round((CurrentValue / TargetValue) * 100, 2);
                
                // تحديث الحالة بناءً على النسبة المئوية
                if (PercentageAchieved == 0)
                    Status = "لم يبدأ";
                else if (PercentageAchieved < 50)
                    Status = "ضعيف";
                else if (PercentageAchieved < 75)
                    Status = "متوسط";
                else if (PercentageAchieved < 100)
                    Status = "جيد";
                else if (PercentageAchieved >= 100)
                    Status = "ممتاز";
            }
            else
            {
                PercentageAchieved = 0;
                Status = "غير محدد";
            }

            LastUpdated = DateTime.Now;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
