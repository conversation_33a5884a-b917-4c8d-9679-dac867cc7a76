﻿#pragma checksum "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "83DB1970988F95B4C09FF1B7F3D77F2C51CE3590"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace TYFManagementSystem.Views {
    
    
    /// <summary>
    /// DatabaseDiagnosticWindow
    /// </summary>
    public partial class DatabaseDiagnosticWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProjectIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DiagnoseButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FixButton;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateTestDataButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReportTextBox;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TYFManagementSystem;component/views/databasediagnosticwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.DiagnoseButton = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
            this.DiagnoseButton.Click += new System.Windows.RoutedEventHandler(this.DiagnoseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.FixButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
            this.FixButton.Click += new System.Windows.RoutedEventHandler(this.FixButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CreateTestDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
            this.CreateTestDataButton.Click += new System.Windows.RoutedEventHandler(this.CreateTestDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\DatabaseDiagnosticWindow.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ReportTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

