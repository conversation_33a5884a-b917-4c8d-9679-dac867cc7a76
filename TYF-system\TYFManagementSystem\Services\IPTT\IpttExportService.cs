using System.Collections.ObjectModel;
using System.Text;
using System.Text.Json;
using System.IO;
using OfficeOpenXml;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة تصدير بيانات IPTT - مسؤولة عن تصدير البيانات بصيغ مختلفة
    /// </summary>
    public class IpttExportService
    {
        /// <summary>
        /// تصدير بيانات IPTT إلى Excel
        /// </summary>
        public async Task<bool> ExportToExcelAsync(
            IpttDataModel dataModel,
            string filePath)
        {
            try
            {
                // Set EPPlus license context
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using var package = new ExcelPackage();

                // إنشاء ورقة عمل لكل موقع
                foreach (var locationKvp in dataModel.LocationData.OrderBy(l => l.Key))
                {
                    string sheetName = locationKvp.Key == 0 ? "الإجمالي" : $"الموقع {locationKvp.Key}";
                    var worksheet = package.Workbook.Worksheets.Add(sheetName);

                    await CreateExcelWorksheetAsync(worksheet, dataModel, locationKvp.Value, locationKvp.Key);
                }

                // حفظ الملف
                var fileInfo = new FileInfo(filePath);
                await package.SaveAsAsync(fileInfo);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تصدير البيانات إلى Excel: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء ورقة عمل Excel
        /// </summary>
        private async Task CreateExcelWorksheetAsync(
            ExcelWorksheet worksheet,
            IpttDataModel dataModel,
            ObservableCollection<IpttDisplayRow> rows,
            int locationId)
        {
            try
            {
                int currentRow = 1;

                // إضافة معلومات المشروع
                worksheet.Cells[currentRow, 1].Value = "اسم المشروع:";
                worksheet.Cells[currentRow, 2].Value = dataModel.ProjectInfo.Name;
                currentRow++;

                worksheet.Cells[currentRow, 1].Value = "مدير المشروع:";
                worksheet.Cells[currentRow, 2].Value = dataModel.ProjectInfo.Manager;
                currentRow++;

                worksheet.Cells[currentRow, 1].Value = "المدة:";
                worksheet.Cells[currentRow, 2].Value = $"من {dataModel.ProjectInfo.StartDate:yyyy/MM/dd} إلى {dataModel.ProjectInfo.EndDate:yyyy/MM/dd}";
                currentRow += 2;

                // إضافة رؤوس الأعمدة
                int col = 1;
                worksheet.Cells[currentRow, col++].Value = "رقم المؤشر";
                worksheet.Cells[currentRow, col++].Value = "المؤشر";
                worksheet.Cells[currentRow, col++].Value = "نوع البيانات";
                worksheet.Cells[currentRow, col++].Value = "الهدف";

                // إضافة أعمدة الأشهر
                foreach (var month in dataModel.MonthColumns)
                {
                    worksheet.Cells[currentRow, col++].Value = month;
                }

                worksheet.Cells[currentRow, col++].Value = "الإنجاز";
                worksheet.Cells[currentRow, col++].Value = "النسبة %";

                // تنسيق رؤوس الأعمدة
                using (var range = worksheet.Cells[currentRow, 1, currentRow, col - 1])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                }

                currentRow++;

                // إضافة البيانات
                foreach (var row in rows)
                {
                    col = 1;
                    worksheet.Cells[currentRow, col++].Value = row.No;
                    worksheet.Cells[currentRow, col++].Value = row.Indicator;
                    worksheet.Cells[currentRow, col++].Value = row.DataType;
                    worksheet.Cells[currentRow, col++].Value = row.Target;

                    // إضافة البيانات الشهرية
                    foreach (var month in dataModel.MonthColumns)
                    {
                        if (row.MonthlyData.ContainsKey(month))
                        {
                            worksheet.Cells[currentRow, col].Value = row.MonthlyData[month];
                        }
                        col++;
                    }

                    worksheet.Cells[currentRow, col++].Value = row.Achievement;
                    worksheet.Cells[currentRow, col++].Value = row.AchievementPercentage;

                    // تنسيق الصفوف
                    if (row.IsMainIndicator)
                    {
                        using (var range = worksheet.Cells[currentRow, 1, currentRow, col - 1])
                        {
                            range.Style.Font.Bold = true;
                            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                        }
                    }
                    else if (row.IsTotalRow)
                    {
                        using (var range = worksheet.Cells[currentRow, 1, currentRow, col - 1])
                        {
                            range.Style.Font.Bold = true;
                            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGreen);
                        }
                    }

                    currentRow++;
                }

                // تعديل عرض الأعمدة
                worksheet.Cells.AutoFitColumns();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء ورقة العمل: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تصدير بيانات IPTT إلى JSON
        /// </summary>
        public async Task<bool> ExportToJsonAsync(
            IpttDataModel dataModel,
            string filePath)
        {
            try
            {
                var exportData = new
                {
                    ProjectInfo = new
                    {
                        Id = dataModel.ProjectInfo.Id,
                        Name = dataModel.ProjectInfo.Name,
                        Manager = dataModel.ProjectInfo.Manager,
                        Status = dataModel.ProjectInfo.Status,
                        StartDate = dataModel.ProjectInfo.StartDate,
                        EndDate = dataModel.ProjectInfo.EndDate,
                        Budget = dataModel.ProjectInfo.Budget,
                        Description = dataModel.ProjectInfo.Description
                    },
                    Indicators = dataModel.Indicators.Select(indicator => new
                    {
                        No = indicator.No,
                        Indicator = indicator.Indicator,
                        DataTypes = indicator.DataTypes.Select(dt => new
                        {
                            Name = dt.Name,
                            Target = dt.Target,
                            MonthlyValues = dt.MonthlyValues
                        }).ToList()
                    }).ToList(),
                    MonthColumns = dataModel.MonthColumns,
                    LocationCount = dataModel.LocationCount,
                    LocationData = dataModel.LocationData.ToDictionary(
                        kvp => kvp.Key.ToString(),
                        kvp => kvp.Value.Select(row => new
                        {
                            No = row.No,
                            Indicator = row.Indicator,
                            DataType = row.DataType,
                            Target = row.Target,
                            Achievement = row.Achievement,
                            AchievementPercentage = row.AchievementPercentage,
                            MonthlyData = row.MonthlyData,
                            IsMainIndicator = row.IsMainIndicator,
                            IsDataTypeRow = row.IsDataTypeRow,
                            IsTotalRow = row.IsTotalRow,
                            IsCompleted = row.IsCompleted,
                            IndicatorId = row.IndicatorId
                        }).ToList()
                    ),
                    ExportDate = DateTime.Now,
                    ExportVersion = "1.0"
                };

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(exportData, options);
                await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تصدير البيانات إلى JSON: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تصدير بيانات IPTT إلى CSV
        /// </summary>
        public async Task<bool> ExportToCsvAsync(
            IpttDataModel dataModel,
            string filePath)
        {
            try
            {
                var csv = new StringBuilder();

                // إضافة معلومات المشروع
                csv.AppendLine($"اسم المشروع,{dataModel.ProjectInfo.Name}");
                csv.AppendLine($"مدير المشروع,{dataModel.ProjectInfo.Manager}");
                csv.AppendLine($"تاريخ البداية,{dataModel.ProjectInfo.StartDate:yyyy/MM/dd}");
                csv.AppendLine($"تاريخ النهاية,{dataModel.ProjectInfo.EndDate:yyyy/MM/dd}");
                csv.AppendLine(); // سطر فارغ

                // تصدير كل موقع في قسم منفصل
                foreach (var locationKvp in dataModel.LocationData.OrderBy(l => l.Key))
                {
                    string locationName = locationKvp.Key == 0 ? "الإجمالي" : $"الموقع {locationKvp.Key}";
                    csv.AppendLine($"=== {locationName} ===");

                    // رؤوس الأعمدة
                    var headers = new List<string>
                    {
                        "رقم المؤشر", "المؤشر", "نوع البيانات", "الهدف"
                    };
                    headers.AddRange(dataModel.MonthColumns);
                    headers.AddRange(new[] { "الإنجاز", "النسبة %" });

                    csv.AppendLine(string.Join(",", headers.Select(h => $"\"{h}\"")));

                    // البيانات
                    foreach (var row in locationKvp.Value)
                    {
                        var values = new List<string>
                        {
                            $"\"{row.No}\"",
                            $"\"{row.Indicator}\"",
                            $"\"{row.DataType}\"",
                            $"\"{row.Target}\""
                        };

                        foreach (var month in dataModel.MonthColumns)
                        {
                            var value = row.MonthlyData.ContainsKey(month) ? row.MonthlyData[month] : "";
                            values.Add($"\"{value}\"");
                        }

                        values.Add($"\"{row.Achievement}\"");
                        values.Add($"\"{row.AchievementPercentage}\"");

                        csv.AppendLine(string.Join(",", values));
                    }

                    csv.AppendLine(); // سطر فارغ بين المواقع
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تصدير البيانات إلى CSV: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من البيانات
        /// </summary>
        public async Task<string> CreateBackupAsync(IpttDataModel dataModel)
        {
            try
            {
                // إنشاء اسم ملف النسخة الاحتياطية
                var sanitizedProjectName = SanitizeFileName(dataModel.ProjectInfo.Name);
                var fileName = $"IPTT_Backup_{sanitizedProjectName}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                
                // إنشاء مجلد النسخ الاحتياطية
                var backupFolder = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    "TYF_IPTT_Backups");

                if (!Directory.Exists(backupFolder))
                {
                    Directory.CreateDirectory(backupFolder);
                }

                var filePath = Path.Combine(backupFolder, fileName);

                // حفظ النسخة الاحتياطية
                await ExportToJsonAsync(dataModel, filePath);

                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء النسخة الاحتياطية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنظيف اسم الملف من الأحرف غير المسموحة
        /// </summary>
        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return string.IsNullOrWhiteSpace(sanitized) ? "Unknown" : sanitized;
        }

        /// <summary>
        /// الحصول على معلومات التصدير
        /// </summary>
        public ExportInfo GetExportInfo(IpttDataModel dataModel)
        {
            return new ExportInfo
            {
                ProjectName = dataModel.ProjectInfo.Name,
                IndicatorCount = dataModel.Indicators.Count,
                LocationCount = dataModel.LocationCount,
                MonthCount = dataModel.MonthColumns.Count,
                TotalRows = dataModel.LocationData.Values.Sum(rows => rows.Count),
                DataTypeCount = dataModel.Indicators.Sum(i => i.DataTypes.Count)
            };
        }
    }

    /// <summary>
    /// معلومات التصدير
    /// </summary>
    public class ExportInfo
    {
        public string ProjectName { get; set; } = "";
        public int IndicatorCount { get; set; }
        public int LocationCount { get; set; }
        public int MonthCount { get; set; }
        public int TotalRows { get; set; }
        public int DataTypeCount { get; set; }
    }
}
