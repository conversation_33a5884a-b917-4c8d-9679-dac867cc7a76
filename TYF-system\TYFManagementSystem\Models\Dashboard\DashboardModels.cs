using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TYFManagementSystem.Models.Dashboard
{
    /// <summary>
    /// إحصائيات لوحة التحكم الشاملة
    /// </summary>
    public class DashboardStatistics : INotifyPropertyChanged
    {
        private int _totalProjects;
        private int _activeProjects;
        private int _completedProjects;
        private int _totalBeneficiaries;
        private int _monthlyReports;
        private int _totalIndicators;
        private int _completedIndicators;
        private DateTime _lastUpdated;

        public int TotalProjects
        {
            get => _totalProjects;
            set => SetProperty(ref _totalProjects, value);
        }

        public int ActiveProjects
        {
            get => _activeProjects;
            set => SetProperty(ref _activeProjects, value);
        }

        public int CompletedProjects
        {
            get => _completedProjects;
            set => SetProperty(ref _completedProjects, value);
        }

        public int TotalBeneficiaries
        {
            get => _totalBeneficiaries;
            set => SetProperty(ref _totalBeneficiaries, value);
        }

        public int MonthlyReports
        {
            get => _monthlyReports;
            set => SetProperty(ref _monthlyReports, value);
        }

        public int TotalIndicators
        {
            get => _totalIndicators;
            set => SetProperty(ref _totalIndicators, value);
        }

        public int CompletedIndicators
        {
            get => _completedIndicators;
            set => SetProperty(ref _completedIndicators, value);
        }

        public DateTime LastUpdated
        {
            get => _lastUpdated;
            set => SetProperty(ref _lastUpdated, value);
        }

        // خصائص محسوبة
        public double ActiveProjectsPercentage { get; set; }
        public double CompletedProjectsPercentage { get; set; }
        public double IndicatorsCompletionPercentage { get; set; }
        public decimal TotalBudget { get; set; }
        public decimal AverageBudgetPerProject { get; set; }
        public double AverageBeneficiariesPerProject { get; set; }
        public int RegionsCount { get; set; }
        public int ProjectsStartingThisMonth { get; set; }
        public int ProjectsEndingThisMonth { get; set; }

        // خصائص منسقة للعرض
        public string TotalBeneficiariesFormatted => $"{TotalBeneficiaries:N0}";
        public string TotalBudgetFormatted => $"{TotalBudget:N0} ريال";
        public string LastUpdatedFormatted => LastUpdated.ToString("yyyy-MM-dd HH:mm");
        public string ActiveProjectsPercentageFormatted => $"{ActiveProjectsPercentage:F1}%";
        public string CompletedProjectsPercentageFormatted => $"{CompletedProjectsPercentage:F1}%";
        public string IndicatorsCompletionPercentageFormatted => $"{IndicatorsCompletionPercentage:F1}%";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// إحصائيات سريعة للكروت
    /// </summary>
    public class QuickStats : INotifyPropertyChanged
    {
        private int _activeProjects;
        private int _totalBeneficiaries;
        private int _monthlyReports;
        private int _activeIndicators;

        public int ActiveProjects
        {
            get => _activeProjects;
            set => SetProperty(ref _activeProjects, value);
        }

        public int TotalBeneficiaries
        {
            get => _totalBeneficiaries;
            set => SetProperty(ref _totalBeneficiaries, value);
        }

        public int MonthlyReports
        {
            get => _monthlyReports;
            set => SetProperty(ref _monthlyReports, value);
        }

        public int ActiveIndicators
        {
            get => _activeIndicators;
            set => SetProperty(ref _activeIndicators, value);
        }

        // خصائص منسقة للعرض
        public string TotalBeneficiariesFormatted => $"{TotalBeneficiaries:N0}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// إحصائيات المشاريع حسب الحالة
    /// </summary>
    public class ProjectStatusStats
    {
        public int Total { get; set; }
        public int Active { get; set; }
        public int Completed { get; set; }
        public int OnHold { get; set; }
        public int Cancelled { get; set; }

        public double ActivePercentage { get; set; }
        public double CompletedPercentage { get; set; }
        public double OnHoldPercentage { get; set; }
        public double CancelledPercentage { get; set; }

        // خصائص منسقة للعرض
        public string ActivePercentageFormatted => $"{ActivePercentage:F1}%";
        public string CompletedPercentageFormatted => $"{CompletedPercentage:F1}%";
        public string OnHoldPercentageFormatted => $"{OnHoldPercentage:F1}%";
        public string CancelledPercentageFormatted => $"{CancelledPercentage:F1}%";
    }

    /// <summary>
    /// نشاط حديث
    /// </summary>
    public class RecentActivity
    {
        public string Type { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string Icon { get; set; } = "";

        public string TimestampFormatted => Timestamp.ToString("yyyy-MM-dd HH:mm");
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.Now - Timestamp;
                if (timeSpan.TotalMinutes < 1)
                    return "الآن";
                else if (timeSpan.TotalMinutes < 60)
                    return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                else if (timeSpan.TotalHours < 24)
                    return $"منذ {(int)timeSpan.TotalHours} ساعة";
                else if (timeSpan.TotalDays < 7)
                    return $"منذ {(int)timeSpan.TotalDays} يوم";
                else
                    return Timestamp.ToString("yyyy-MM-dd");
            }
        }
    }

    /// <summary>
    /// بيانات الكرت في لوحة التحكم
    /// </summary>
    public class DashboardCard : INotifyPropertyChanged
    {
        private string _title = "";
        private string _value = "";
        private string _icon = "";
        private string _color = "";
        private string _targetView = "";

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public string Value
        {
            get => _value;
            set => SetProperty(ref _value, value);
        }

        public string Icon
        {
            get => _icon;
            set => SetProperty(ref _icon, value);
        }

        public string Color
        {
            get => _color;
            set => SetProperty(ref _color, value);
        }

        public string TargetView
        {
            get => _targetView;
            set => SetProperty(ref _targetView, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// معلومات التنقل للكروت
    /// </summary>
    public static class DashboardNavigation
    {
        public const string PROJECTS_VIEW = "المشاريع";
        public const string REPORTS_VIEW = "التقارير";
        public const string ME_VIEW = "المتابعة والتقييم";
        public const string DATA_MANAGEMENT_VIEW = "إدارة البيانات";
    }
}
