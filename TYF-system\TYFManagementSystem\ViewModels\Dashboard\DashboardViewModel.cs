using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.Dashboard;
using TYFManagementSystem.Services;

namespace TYFManagementSystem.ViewModels.Dashboard
{
    /// <summary>
    /// ViewModel للوحة التحكم الرئيسية
    /// </summary>
    public class DashboardViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly DashboardService _dashboardService;
        private DashboardStatistics? _statistics;
        private QuickStats? _quickStats;
        private ProjectStatusStats? _projectStatusStats;
        private ObservableCollection<DashboardCard> _dashboardCards;
        private ObservableCollection<RecentActivity> _recentActivities;
        private bool _isLoading;
        private string _statusMessage = "";
        private DateTime _lastRefresh;
        #endregion

        #region Properties
        public DashboardStatistics? Statistics
        {
            get => _statistics;
            set => SetProperty(ref _statistics, value);
        }

        public QuickStats? QuickStats
        {
            get => _quickStats;
            set => SetProperty(ref _quickStats, value);
        }

        public ProjectStatusStats? ProjectStatusStats
        {
            get => _projectStatusStats;
            set => SetProperty(ref _projectStatusStats, value);
        }

        public ObservableCollection<DashboardCard> DashboardCards
        {
            get => _dashboardCards;
            set => SetProperty(ref _dashboardCards, value);
        }

        public ObservableCollection<RecentActivity> RecentActivities
        {
            get => _recentActivities;
            set => SetProperty(ref _recentActivities, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public DateTime LastRefresh
        {
            get => _lastRefresh;
            set => SetProperty(ref _lastRefresh, value);
        }

        public string LastRefreshFormatted => LastRefresh.ToString("yyyy-MM-dd HH:mm:ss");
        #endregion

        #region Commands
        public ICommand LoadDashboardCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand CardClickCommand { get; }
        #endregion

        #region Events
        public event Action<string>? NavigationRequested;
        #endregion

        #region Constructor
        public DashboardViewModel()
        {
            _dashboardService = new DashboardService();
            _dashboardCards = new ObservableCollection<DashboardCard>();
            _recentActivities = new ObservableCollection<RecentActivity>();

            // Initialize commands
            LoadDashboardCommand = new RelayCommand(async _ => await LoadDashboardAsync());
            RefreshCommand = new RelayCommand(async _ => await RefreshDashboardAsync());
            CardClickCommand = new RelayCommand(OnCardClick);

            // Load initial data
            _ = Task.Run(LoadDashboardAsync);
        }
        #endregion

        #region Private Methods
        private async Task LoadDashboardAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل بيانات لوحة التحكم...";

                // تحميل الإحصائيات
                await LoadStatisticsAsync();

                // تحميل الإحصائيات السريعة
                await LoadQuickStatsAsync();

                // تحميل إحصائيات حالة المشاريع
                await LoadProjectStatusStatsAsync();

                // إنشاء كروت لوحة التحكم
                CreateDashboardCards();

                // تحميل الأنشطة الحديثة
                await LoadRecentActivitiesAsync();

                LastRefresh = DateTime.Now;
                StatusMessage = "تم تحميل بيانات لوحة التحكم بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "فشل في تحميل بيانات لوحة التحكم";
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات لوحة التحكم: {ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadStatisticsAsync()
        {
            Statistics = await _dashboardService.GetDashboardStatisticsAsync();
        }

        private async Task LoadQuickStatsAsync()
        {
            QuickStats = await _dashboardService.GetQuickStatsAsync();
        }

        private async Task LoadProjectStatusStatsAsync()
        {
            ProjectStatusStats = await _dashboardService.GetProjectStatusStatsAsync();
        }

        private void CreateDashboardCards()
        {
            DashboardCards.Clear();

            if (QuickStats != null)
            {
                // كرت المشاريع النشطة
                DashboardCards.Add(new DashboardCard
                {
                    Title = "المشاريع النشطة",
                    Value = QuickStats.ActiveProjects.ToString(),
                    Icon = "📁",
                    Color = "#2E7D32",
                    TargetView = DashboardNavigation.PROJECTS_VIEW
                });

                // كرت المستفيدين
                DashboardCards.Add(new DashboardCard
                {
                    Title = "المستفيدين",
                    Value = QuickStats.TotalBeneficiariesFormatted,
                    Icon = "👥",
                    Color = "#4CAF50",
                    TargetView = DashboardNavigation.PROJECTS_VIEW
                });

                // كرت التقارير الشهرية
                DashboardCards.Add(new DashboardCard
                {
                    Title = "التقارير الشهرية",
                    Value = QuickStats.MonthlyReports.ToString(),
                    Icon = "📊",
                    Color = "#81C784",
                    TargetView = DashboardNavigation.REPORTS_VIEW
                });

                // كرت المؤشرات النشطة
                DashboardCards.Add(new DashboardCard
                {
                    Title = "المؤشرات النشطة",
                    Value = QuickStats.ActiveIndicators.ToString(),
                    Icon = "📈",
                    Color = "#FF9800",
                    TargetView = DashboardNavigation.ME_VIEW
                });
            }
        }

        private async Task LoadRecentActivitiesAsync()
        {
            try
            {
                var activities = await _dashboardService.GetRecentActivitiesAsync(10);
                
                RecentActivities.Clear();
                foreach (var activity in activities)
                {
                    RecentActivities.Add(activity);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الأنشطة الحديثة: {ex.Message}");
            }
        }

        private async Task RefreshDashboardAsync()
        {
            await LoadDashboardAsync();
        }

        private void OnCardClick(object? parameter)
        {
            if (parameter is DashboardCard card && !string.IsNullOrEmpty(card.TargetView))
            {
                NavigationRequested?.Invoke(card.TargetView);
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// تحديث البيانات عند تغيير قاعدة البيانات
        /// </summary>
        public async Task RefreshDataAsync()
        {
            await RefreshDashboardAsync();
        }

        /// <summary>
        /// الحصول على إحصائيات محددة
        /// </summary>
        public async Task<T?> GetStatisticsAsync<T>() where T : class
        {
            try
            {
                if (typeof(T) == typeof(DashboardStatistics))
                    return await _dashboardService.GetDashboardStatisticsAsync() as T;
                
                if (typeof(T) == typeof(QuickStats))
                    return await _dashboardService.GetQuickStatsAsync() as T;
                
                if (typeof(T) == typeof(ProjectStatusStats))
                    return await _dashboardService.GetProjectStatusStatsAsync() as T;

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الإحصائيات: {ex.Message}");
                return null;
            }
        }
        #endregion

        #region IDisposable
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _dashboardService?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
