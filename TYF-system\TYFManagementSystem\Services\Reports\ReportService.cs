using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models.Reports;
using System.Text.Json;

namespace TYFManagementSystem.Services.Reports
{
    /// <summary>
    /// خدمة إدارة التقارير
    /// </summary>
    public class ReportService
    {
        private readonly TyfDbContext _context;

        public ReportService()
        {
            _context = new TyfDbContext();
        }

        /// <summary>
        /// الحصول على جميع التقارير
        /// </summary>
        public async Task<List<Report>> GetAllReportsAsync()
        {
            try
            {
                return await _context.Reports
                    .OrderByDescending(r => r.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب التقارير: {ex.Message}");
                return new List<Report>();
            }
        }

        /// <summary>
        /// البحث في التقارير
        /// </summary>
        public async Task<List<Report>> SearchReportsAsync(string searchTerm, ReportType? type = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var query = _context.Reports.AsQueryable();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(r => r.Name.Contains(searchTerm) || 
                                           r.Description.Contains(searchTerm) ||
                                           r.Notes.Contains(searchTerm));
                }

                if (type.HasValue)
                {
                    query = query.Where(r => r.Type == type.Value);
                }

                if (startDate.HasValue)
                {
                    query = query.Where(r => r.CreatedDate >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(r => r.CreatedDate <= endDate.Value);
                }

                return await query.OrderByDescending(r => r.CreatedDate).ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث: {ex.Message}");
                return new List<Report>();
            }
        }

        /// <summary>
        /// حفظ تقرير جديد
        /// </summary>
        public async Task<bool> SaveReportAsync(Report report)
        {
            try
            {
                if (string.IsNullOrEmpty(report.Id))
                {
                    report.Id = Guid.NewGuid().ToString();
                    report.CreatedDate = DateTime.Now;
                    _context.Reports.Add(report);
                }
                else
                {
                    report.LastModified = DateTime.Now;
                    _context.Reports.Update(report);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ التقرير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف تقرير
        /// </summary>
        public async Task<bool> DeleteReportAsync(string reportId)
        {
            try
            {
                var report = await _context.Reports.FindAsync(reportId);
                if (report != null)
                {
                    _context.Reports.Remove(report);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف التقرير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على تقرير بالمعرف
        /// </summary>
        public async Task<Report?> GetReportByIdAsync(string reportId)
        {
            try
            {
                return await _context.Reports.FindAsync(reportId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب التقرير: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء تقرير IPTT
        /// </summary>
        public async Task<ReportGenerationResult> GenerateIpttReportAsync(ReportCriteria criteria)
        {
            var result = new ReportGenerationResult();
            var startTime = DateTime.Now;

            try
            {
                var data = new List<ReportDataRow>();

                // جلب بيانات IPTT حسب المعايير
                var query = _context.IpttProjects.AsQueryable();

                if (criteria.ProjectIds.Any())
                {
                    query = query.Where(p => criteria.ProjectIds.Contains(p.ProjectId));
                }

                var projects = await query.Include(p => p.Indicators)
                                         .ThenInclude(i => i.DataTypes)
                                         .ToListAsync();

                foreach (var project in projects)
                {
                    foreach (var indicator in project.Indicators)
                    {
                        var row = new ReportDataRow();
                        row.Values["المشروع"] = project.ProjectName;
                        row.Values["المؤشر"] = indicator.IndicatorName;
                        row.Values["الرقم"] = indicator.IndicatorNumber;
                        row.Values["الهدف الإجمالي"] = indicator.TotalTarget;
                        row.Values["الإنجاز"] = indicator.Achievement;
                        row.Values["نسبة الإنجاز"] = indicator.AchievementPercentage;
                        row.Values["أنواع البيانات"] = indicator.DataTypes.Count;
                        
                        data.Add(row);
                    }
                }

                // إنشاء التقرير
                var report = new Report
                {
                    Name = criteria.Name,
                    Type = criteria.Type,
                    Description = criteria.Description,
                    Criteria = JsonSerializer.Serialize(criteria),
                    Data = JsonSerializer.Serialize(data),
                    Status = ReportStatus.Generated,
                    RecordCount = data.Count,
                    StartDate = criteria.StartDate,
                    EndDate = criteria.EndDate
                };

                result.Success = true;
                result.Report = report;
                result.Data = data;
                result.RecordCount = data.Count;
                result.GenerationTime = DateTime.Now - startTime;

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"خطأ في إنشاء التقرير: {ex.Message}";
                return result;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
