using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.Reports;
using TYFManagementSystem.Services.Reports;

namespace TYFManagementSystem.ViewModels.Reports
{
    /// <summary>
    /// ViewModel لإدارة التقارير
    /// </summary>
    public class ReportsManagementViewModel : BaseViewModel, IDisposable
    {
        #region Private Fields
        private readonly ReportService _reportService;
        private ObservableCollection<Report> _reports;
        private ObservableCollection<Report> _filteredReports;
        private Report? _selectedReport;
        private string _searchText = "";
        private ReportType? _selectedReportType;
        private DateTime? _startDate;
        private DateTime? _endDate;
        private bool _isLoading;
        private string _statusMessage = "";
        #endregion

        #region Properties
        public ObservableCollection<Report> Reports
        {
            get => _reports;
            set => SetProperty(ref _reports, value);
        }

        public ObservableCollection<Report> FilteredReports
        {
            get => _filteredReports;
            set => SetProperty(ref _filteredReports, value);
        }

        public Report? SelectedReport
        {
            get => _selectedReport;
            set => SetProperty(ref _selectedReport, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public ReportType? SelectedReportType
        {
            get => _selectedReportType;
            set
            {
                if (SetProperty(ref _selectedReportType, value))
                {
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public DateTime? StartDate
        {
            get => _startDate;
            set
            {
                if (SetProperty(ref _startDate, value))
                {
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set
            {
                if (SetProperty(ref _endDate, value))
                {
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public Array ReportTypes => Enum.GetValues(typeof(ReportType));
        #endregion

        #region Commands
        public ICommand LoadReportsCommand { get; private set; }
        public ICommand CreateNewReportCommand { get; private set; }
        public ICommand PreviewReportCommand { get; private set; }
        public ICommand ExportReportCommand { get; private set; }
        public ICommand DeleteReportCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand ClearFiltersCommand { get; private set; }
        #endregion

        #region Constructor
        public ReportsManagementViewModel()
        {
            _reportService = new ReportService();
            _reports = new ObservableCollection<Report>();
            _filteredReports = new ObservableCollection<Report>();

            InitializeCommands();
            _ = LoadReportsAsync();
        }
        #endregion

        #region Private Methods
        private void InitializeCommands()
        {
            LoadReportsCommand = new RelayCommand(async () => await LoadReportsAsync());
            CreateNewReportCommand = new RelayCommand(async () => await CreateNewReportAsync());
            PreviewReportCommand = new RelayCommand(async () => await PreviewReportAsync(), () => SelectedReport != null);
            ExportReportCommand = new RelayCommand(async () => await ExportReportAsync(), () => SelectedReport != null);
            DeleteReportCommand = new RelayCommand(async () => await DeleteReportAsync(), () => SelectedReport != null);
            RefreshCommand = new RelayCommand(async () => await RefreshAsync());
            ClearFiltersCommand = new RelayCommand(() => ClearFilters());
        }

        private async Task LoadReportsAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل التقارير...";

                var reports = await _reportService.GetAllReportsAsync();
                
                Reports.Clear();
                foreach (var report in reports)
                {
                    Reports.Add(report);
                }

                await ApplyFiltersAsync();
                StatusMessage = $"تم تحميل {reports.Count} تقرير";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل التقارير: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ApplyFiltersAsync()
        {
            try
            {
                var filtered = await _reportService.SearchReportsAsync(
                    SearchText, 
                    SelectedReportType, 
                    StartDate, 
                    EndDate);

                FilteredReports.Clear();
                foreach (var report in filtered)
                {
                    FilteredReports.Add(report);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تطبيق الفلاتر: {ex.Message}";
            }
        }

        private async Task CreateNewReportAsync()
        {
            try
            {
                StatusMessage = "فتح نافذة إنشاء تقرير جديد...";

                var createDialog = new TYFManagementSystem.Views.Reports.CreateReportDialog();
                var result = createDialog.ShowDialog();

                if (result == true && createDialog.CreatedReport != null)
                {
                    // إضافة التقرير الجديد للقائمة
                    Reports.Insert(0, createDialog.CreatedReport);
                    await ApplyFiltersAsync();
                    StatusMessage = $"تم إنشاء التقرير '{createDialog.CreatedReport.Name}' بنجاح";
                }
                else
                {
                    StatusMessage = "تم إلغاء إنشاء التقرير";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إنشاء التقرير: {ex.Message}";
            }
        }

        private async Task PreviewReportAsync()
        {
            if (SelectedReport == null) return;

            try
            {
                StatusMessage = "جاري تحضير معاينة التقرير...";

                // إنشاء نتيجة تقرير مؤقتة للمعاينة
                var result = new ReportGenerationResult
                {
                    Success = true,
                    Report = SelectedReport,
                    RecordCount = SelectedReport.RecordCount,
                    GenerationTime = TimeSpan.FromSeconds(0.1),
                    Data = new List<ReportDataRow>()
                };

                // إذا كان التقرير يحتوي على بيانات محفوظة، قم بتحميلها
                if (!string.IsNullOrEmpty(SelectedReport.Data))
                {
                    try
                    {
                        result.Data = System.Text.Json.JsonSerializer.Deserialize<List<ReportDataRow>>(SelectedReport.Data) ?? new List<ReportDataRow>();
                    }
                    catch
                    {
                        // إذا فشل في تحميل البيانات، أنشئ بيانات تجريبية
                        result.Data = CreateSampleData();
                    }
                }
                else
                {
                    result.Data = CreateSampleData();
                }

                var previewDialog = new TYFManagementSystem.Views.Reports.ReportPreviewDialog(result);
                previewDialog.ShowDialog();

                StatusMessage = "تم عرض معاينة التقرير";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في المعاينة: {ex.Message}";
            }
        }

        private List<ReportDataRow> CreateSampleData()
        {
            return new List<ReportDataRow>
            {
                new ReportDataRow
                {
                    Values = new Dictionary<string, object>
                    {
                        ["المشروع"] = SelectedReport?.ProjectId ?? "مشروع تجريبي",
                        ["المؤشر"] = "مؤشر تجريبي",
                        ["الرقم"] = "1.1",
                        ["الهدف الإجمالي"] = "100",
                        ["الإنجاز"] = "75",
                        ["نسبة الإنجاز"] = "75%",
                        ["أنواع البيانات"] = "3"
                    }
                }
            };
        }

        private async Task ExportReportAsync()
        {
            if (SelectedReport == null) return;

            try
            {
                StatusMessage = "تحضير التقرير للتصدير...";

                // إنشاء نتيجة تقرير للتصدير
                var result = new ReportGenerationResult
                {
                    Success = true,
                    Report = SelectedReport,
                    RecordCount = SelectedReport.RecordCount,
                    GenerationTime = TimeSpan.FromSeconds(0.1),
                    Data = new List<ReportDataRow>()
                };

                // تحميل البيانات إذا كانت متوفرة
                if (!string.IsNullOrEmpty(SelectedReport.Data))
                {
                    try
                    {
                        result.Data = System.Text.Json.JsonSerializer.Deserialize<List<ReportDataRow>>(SelectedReport.Data) ?? new List<ReportDataRow>();
                    }
                    catch
                    {
                        result.Data = CreateSampleData();
                    }
                }
                else
                {
                    result.Data = CreateSampleData();
                }

                var exportDialog = new TYFManagementSystem.Views.Reports.ExportReportDialog(result);
                var exportResult = exportDialog.ShowDialog();

                if (exportResult == true)
                {
                    StatusMessage = "تم تصدير التقرير بنجاح";
                }
                else
                {
                    StatusMessage = "تم إلغاء عملية التصدير";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التصدير: {ex.Message}";
            }
        }

        private async Task DeleteReportAsync()
        {
            if (SelectedReport == null) return;

            try
            {
                StatusMessage = "جاري حذف التقرير...";
                
                var success = await _reportService.DeleteReportAsync(SelectedReport.Id);
                if (success)
                {
                    Reports.Remove(SelectedReport);
                    FilteredReports.Remove(SelectedReport);
                    SelectedReport = null;
                    StatusMessage = "تم حذف التقرير بنجاح";
                }
                else
                {
                    StatusMessage = "فشل في حذف التقرير";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في الحذف: {ex.Message}";
            }
        }

        private async Task RefreshAsync()
        {
            await LoadReportsAsync();
        }

        private void ClearFilters()
        {
            SearchText = "";
            SelectedReportType = null;
            StartDate = null;
            EndDate = null;
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            _reportService?.Dispose();
        }
        #endregion
    }
}
