using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Views.IPTT
{
    public partial class AddIndicatorDialog : Window
    {
        private ObservableCollection<DataTypeItem> dataTypes;

        public AddIndicatorDialog()
        {
            InitializeComponent();
            dataTypes = new ObservableCollection<DataTypeItem>();
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            // Add initial data type row
            AddDataTypeRow();
        }

        private void AddDataTypeRowButton_Click(object sender, RoutedEventArgs e)
        {
            AddDataTypeRow();
        }

        private void AddDataTypeRow()
        {
            var dataTypePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 5)
            };

            var nameLabel = new TextBlock
            {
                Text = "نوع البيانات:",
                Width = 80,
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.Bold,
                FontSize = 12
            };

            var nameTextBox = new TextBox
            {
                Width = 150,
                Height = 25,
                Margin = new Thickness(5, 0, 5, 0),
                Padding = new Thickness(5),
                FontSize = 11
            };

            var targetLabel = new TextBlock
            {
                Text = "الهدف:",
                Width = 50,
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Margin = new Thickness(10, 0, 5, 0)
            };

            var targetTextBox = new TextBox
            {
                Width = 80,
                Height = 25,
                Margin = new Thickness(5, 0, 5, 0),
                Padding = new Thickness(5),
                FontSize = 11
            };

            var removeButton = new Button
            {
                Content = "🗑️",
                Width = 30,
                Height = 25,
                Background = System.Windows.Media.Brushes.Red,
                Foreground = System.Windows.Media.Brushes.White,
                BorderThickness = new Thickness(0),
                Margin = new Thickness(5, 0, 0, 0),
                Cursor = System.Windows.Input.Cursors.Hand
            };

            removeButton.Click += (s, e) =>
            {
                DataTypesPanel.Children.Remove(dataTypePanel);
            };

            dataTypePanel.Children.Add(nameLabel);
            dataTypePanel.Children.Add(nameTextBox);
            dataTypePanel.Children.Add(targetLabel);
            dataTypePanel.Children.Add(targetTextBox);
            dataTypePanel.Children.Add(removeButton);

            DataTypesPanel.Children.Add(dataTypePanel);
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(IndicatorNumberTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال رقم المؤشر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    IndicatorNumberTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(IndicatorNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المؤشر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    IndicatorNameTextBox.Focus();
                    return;
                }

                // Collect data types
                dataTypes.Clear();
                foreach (StackPanel panel in DataTypesPanel.Children)
                {
                    if (panel.Children.Count >= 4)
                    {
                        var nameTextBox = panel.Children[1] as TextBox;
                        var targetTextBox = panel.Children[3] as TextBox;

                        if (nameTextBox != null && targetTextBox != null &&
                            !string.IsNullOrWhiteSpace(nameTextBox.Text))
                        {
                            dataTypes.Add(new DataTypeItem
                            {
                                Name = nameTextBox.Text.Trim(),
                                Target = targetTextBox.Text.Trim(),
                                MonthlyValues = new Dictionary<string, string>() // تهيئة فارغة
                            });
                        }
                    }
                }

                if (dataTypes.Count == 0)
                {
                    MessageBox.Show("يرجى إضافة نوع بيانات واحد على الأقل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", 
                              "خطأ", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        public IpttIndicator GetIndicator()
        {
            if (DialogResult == true)
            {
                return new IpttIndicator
                {
                    No = IndicatorNumberTextBox.Text.Trim(),
                    Indicator = IndicatorNameTextBox.Text.Trim(),
                    DataTypes = dataTypes
                };
            }
            return null;
        }
    }
}
