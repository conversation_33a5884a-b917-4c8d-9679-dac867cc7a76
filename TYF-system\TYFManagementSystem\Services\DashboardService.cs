using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models.Dashboard;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة لوحة التحكم الرئيسية
    /// </summary>
    public class DashboardService
    {
        private readonly TyfDbContext _context;

        public DashboardService()
        {
            _context = new TyfDbContext();
            InitializeDatabaseAsync().Wait();
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.EnsureDatabaseCreatedAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات لوحة التحكم
        /// </summary>
        public async Task<DashboardStatistics> GetDashboardStatisticsAsync()
        {
            try
            {
                var stats = new DashboardStatistics();

                // إحصائيات المشاريع
                var projects = await _context.Projects.ToListAsync();
                stats.TotalProjects = projects.Count;
                stats.ActiveProjects = projects.Count(p => p.Status == "نشط" || p.Status == "Active" || p.Status == "جاري");
                stats.CompletedProjects = projects.Count(p => p.Status == "مكتمل" || p.Status == "Completed" || p.Status == "منتهي");

                // إحصائيات المستفيدين
                stats.TotalBeneficiaries = projects.Sum(p => p.Beneficiaries);

                // إحصائيات التقارير
                stats.MonthlyReports = await _context.Reports
                    .CountAsync(r => r.CreatedDate.Month == DateTime.Now.Month && 
                                   r.CreatedDate.Year == DateTime.Now.Year);

                // إحصائيات IPTT
                var ipttProjects = await _context.IpttProjects.Include(p => p.Indicators).ToListAsync();
                stats.TotalIndicators = ipttProjects.Sum(p => p.Indicators.Count);

                // حساب المؤشرات المكتملة
                stats.CompletedIndicators = 0;
                foreach (var ipttProject in ipttProjects)
                {
                    foreach (var indicator in ipttProject.Indicators)
                    {
                        var monthlyData = await _context.IpttMonthlyData
                            .Where(m => m.IndicatorId == indicator.Id)
                            .ToListAsync();

                        if (monthlyData.Any(m => !string.IsNullOrEmpty(m.Value) && m.Value != "0"))
                        {
                            stats.CompletedIndicators++;
                        }
                    }
                }

                // حساب النسب المئوية
                stats.ActiveProjectsPercentage = stats.TotalProjects > 0 
                    ? Math.Round((double)stats.ActiveProjects / stats.TotalProjects * 100, 1) 
                    : 0;

                stats.CompletedProjectsPercentage = stats.TotalProjects > 0 
                    ? Math.Round((double)stats.CompletedProjects / stats.TotalProjects * 100, 1) 
                    : 0;

                stats.IndicatorsCompletionPercentage = stats.TotalIndicators > 0 
                    ? Math.Round((double)stats.CompletedIndicators / stats.TotalIndicators * 100, 1) 
                    : 0;

                // إحصائيات إضافية
                stats.TotalBudget = projects.Sum(p => p.Budget);
                stats.AverageBudgetPerProject = stats.TotalProjects > 0 
                    ? stats.TotalBudget / stats.TotalProjects 
                    : 0;

                stats.AverageBeneficiariesPerProject = stats.TotalProjects > 0 
                    ? (double)stats.TotalBeneficiaries / stats.TotalProjects 
                    : 0;

                // إحصائيات المناطق
                stats.RegionsCount = projects.Where(p => !string.IsNullOrEmpty(p.Region))
                    .Select(p => p.Region).Distinct().Count();

                // إحصائيات زمنية
                var currentDate = DateTime.Now;
                stats.ProjectsStartingThisMonth = projects.Count(p => 
                    p.StartDate.Month == currentDate.Month && p.StartDate.Year == currentDate.Year);

                stats.ProjectsEndingThisMonth = projects.Count(p => 
                    p.EndDate.Month == currentDate.Month && p.EndDate.Year == currentDate.Year);

                stats.LastUpdated = DateTime.Now;

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب إحصائيات لوحة التحكم: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات سريعة للكروت
        /// </summary>
        public async Task<QuickStats> GetQuickStatsAsync()
        {
            try
            {
                var stats = new QuickStats();

                // المشاريع النشطة
                stats.ActiveProjects = await _context.Projects
                    .CountAsync(p => p.Status == "نشط" || p.Status == "Active" || p.Status == "جاري");

                // إجمالي المستفيدين
                stats.TotalBeneficiaries = await _context.Projects.SumAsync(p => p.Beneficiaries);

                // التقارير الشهرية
                stats.MonthlyReports = await _context.Reports
                    .CountAsync(r => r.CreatedDate.Month == DateTime.Now.Month && 
                                   r.CreatedDate.Year == DateTime.Now.Year);

                // المؤشرات النشطة (التي لها بيانات)
                var ipttProjects = await _context.IpttProjects.Include(p => p.Indicators).ToListAsync();
                stats.ActiveIndicators = 0;

                foreach (var ipttProject in ipttProjects)
                {
                    foreach (var indicator in ipttProject.Indicators)
                    {
                        var hasData = await _context.IpttMonthlyData
                            .AnyAsync(m => m.IndicatorId == indicator.Id && 
                                         !string.IsNullOrEmpty(m.Value) && m.Value != "0");

                        if (hasData)
                        {
                            stats.ActiveIndicators++;
                        }
                    }
                }

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب الإحصائيات السريعة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المشاريع حسب الحالة
        /// </summary>
        public async Task<ProjectStatusStats> GetProjectStatusStatsAsync()
        {
            try
            {
                var projects = await _context.Projects.ToListAsync();
                var stats = new ProjectStatusStats();

                stats.Total = projects.Count;
                stats.Active = projects.Count(p => p.Status == "نشط" || p.Status == "Active" || p.Status == "جاري");
                stats.Completed = projects.Count(p => p.Status == "مكتمل" || p.Status == "Completed" || p.Status == "منتهي");
                stats.OnHold = projects.Count(p => p.Status == "متوقف" || p.Status == "OnHold" || p.Status == "معلق");
                stats.Cancelled = projects.Count(p => p.Status == "ملغي" || p.Status == "Cancelled");

                // حساب النسب المئوية
                if (stats.Total > 0)
                {
                    stats.ActivePercentage = Math.Round((double)stats.Active / stats.Total * 100, 1);
                    stats.CompletedPercentage = Math.Round((double)stats.Completed / stats.Total * 100, 1);
                    stats.OnHoldPercentage = Math.Round((double)stats.OnHold / stats.Total * 100, 1);
                    stats.CancelledPercentage = Math.Round((double)stats.Cancelled / stats.Total * 100, 1);
                }

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب إحصائيات حالة المشاريع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على أحدث الأنشطة
        /// </summary>
        public async Task<RecentActivity[]> GetRecentActivitiesAsync(int count = 5)
        {
            try
            {
                var activities = new List<RecentActivity>();

                // أحدث المشاريع
                var recentProjects = await _context.Projects
                    .OrderByDescending(p => p.CreatedDate)
                    .Take(count / 2)
                    .ToListAsync();

                foreach (var project in recentProjects)
                {
                    activities.Add(new RecentActivity
                    {
                        Type = "مشروع جديد",
                        Description = $"تم إضافة مشروع: {project.Name}",
                        Timestamp = project.CreatedDate,
                        Icon = "📁"
                    });
                }

                // أحدث التقارير
                var recentReports = await _context.Reports
                    .OrderByDescending(r => r.CreatedDate)
                    .Take(count / 2)
                    .ToListAsync();

                foreach (var report in recentReports)
                {
                    activities.Add(new RecentActivity
                    {
                        Type = "تقرير جديد",
                        Description = $"تم إنشاء تقرير: {report.Name}",
                        Timestamp = report.CreatedDate,
                        Icon = "📊"
                    });
                }

                return activities.OrderByDescending(a => a.Timestamp).Take(count).ToArray();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب الأنشطة الحديثة: {ex.Message}", ex);
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
