﻿#pragma checksum "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "19330E2C40367637D4631EF9129FA08E262DE115"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using TYFManagementSystem.ViewModels.DataManagement;


namespace TYFManagementSystem.Views.IPTT {
    
    
    /// <summary>
    /// AddIndicatorDialog
    /// </summary>
    public partial class AddIndicatorDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IndicatorNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IndicatorNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DataTypesPanel;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDataTypeRowButton;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TYFManagementSystem;component/views/iptt/addindicatordialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.IndicatorNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.IndicatorNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.DataTypesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.AddDataTypeRowButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
            this.AddDataTypeRowButton.Click += new System.Windows.RoutedEventHandler(this.AddDataTypeRowButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\..\Views\IPTT\AddIndicatorDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

