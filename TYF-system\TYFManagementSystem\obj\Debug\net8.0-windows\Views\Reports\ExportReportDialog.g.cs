﻿#pragma checksum "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D33F7B3C9C22CF3A2D5F3F452278320B129FB586"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace TYFManagementSystem.Views.Reports {
    
    
    /// <summary>
    /// ExportReportDialog
    /// </summary>
    public partial class ExportReportDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 26 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReportTitleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecordCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstimatedSizeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ExcelRadioButton;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CsvRadioButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton JsonRadioButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton XmlRadioButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton PdfRadioButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeHeadersCheckBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeReportInfoCheckBox;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OpenAfterExportCheckBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox EncodingComboBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileNamePreviewTextBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TYFManagementSystem;component/views/reports/exportreportdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ReportNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ReportTitleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.RecordCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.EstimatedSizeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ExcelRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 6:
            this.CsvRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 7:
            this.JsonRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 8:
            this.XmlRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 9:
            this.PdfRadioButton = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 10:
            this.IncludeHeadersCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.IncludeReportInfoCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.OpenAfterExportCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.EncodingComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.FileNamePreviewTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\..\..\Views\Reports\ExportReportDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

