using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.DataManagement;
using TYFManagementSystem.Models.Reports;
using TYFManagementSystem.Services;

namespace TYFManagementSystem.ViewModels.DataManagement
{
    /// <summary>
    /// ViewModel لإدارة البيانات والمعلومات
    /// </summary>
    public class DataManagementViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly DataManagementService _dataService;
        private ObservableCollection<ProjectSummary> _projects;
        private ObservableCollection<Report> _reports;
        private ObservableCollection<string> _availableRegions;
        private ObservableCollection<string> _availableStatuses;
        private ProjectSummary? _selectedProject;
        private Report? _selectedReport;
        private DatabaseStatistics? _databaseStats;
        private SearchCriteria _searchCriteria;
        private bool _isLoading;
        private string _statusMessage = "";
        private string _selectedTab = "المشاريع";
        #endregion

        #region Properties
        public ObservableCollection<ProjectSummary> Projects
        {
            get => _projects;
            set => SetProperty(ref _projects, value);
        }

        public ObservableCollection<Report> Reports
        {
            get => _reports;
            set => SetProperty(ref _reports, value);
        }

        public ObservableCollection<string> AvailableRegions
        {
            get => _availableRegions;
            set => SetProperty(ref _availableRegions, value);
        }

        public ObservableCollection<string> AvailableStatuses
        {
            get => _availableStatuses;
            set => SetProperty(ref _availableStatuses, value);
        }

        public ProjectSummary? SelectedProject
        {
            get => _selectedProject;
            set => SetProperty(ref _selectedProject, value);
        }

        public Report? SelectedReport
        {
            get => _selectedReport;
            set => SetProperty(ref _selectedReport, value);
        }

        public DatabaseStatistics? DatabaseStats
        {
            get => _databaseStats;
            set => SetProperty(ref _databaseStats, value);
        }

        public SearchCriteria SearchCriteria
        {
            get => _searchCriteria;
            set => SetProperty(ref _searchCriteria, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string SelectedTab
        {
            get => _selectedTab;
            set => SetProperty(ref _selectedTab, value);
        }
        #endregion

        #region Commands
        public ICommand LoadDataCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ClearSearchCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand DeleteReportCommand { get; }
        public ICommand ViewProjectDetailsCommand { get; }
        public ICommand ExportDataCommand { get; }
        public ICommand TabChangedCommand { get; }
        #endregion

        #region Constructor
        public DataManagementViewModel()
        {
            _dataService = new DataManagementService();
            _projects = new ObservableCollection<ProjectSummary>();
            _reports = new ObservableCollection<Report>();
            _availableRegions = new ObservableCollection<string>();
            _availableStatuses = new ObservableCollection<string>();
            _searchCriteria = new SearchCriteria();

            // Initialize commands
            LoadDataCommand = new RelayCommand(async _ => await LoadDataAsync());
            SearchCommand = new RelayCommand(async _ => await SearchAsync());
            ClearSearchCommand = new RelayCommand(_ => ClearSearch());
            RefreshCommand = new RelayCommand(async _ => await RefreshDataAsync());
            DeleteReportCommand = new RelayCommand(async _ => await DeleteSelectedReportAsync(), _ => SelectedReport != null);
            ViewProjectDetailsCommand = new RelayCommand(ViewProjectDetails, _ => SelectedProject != null);
            ExportDataCommand = new RelayCommand(async _ => await ExportDataAsync());
            TabChangedCommand = new RelayCommand(async parameter => await OnTabChangedAsync(parameter?.ToString()));

            // Load initial data
            _ = Task.Run(LoadDataAsync);
        }
        #endregion

        #region Private Methods
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل البيانات...";

                // تحميل البيانات الأساسية
                await LoadProjectsAsync();
                await LoadFiltersAsync();
                await LoadDatabaseStatsAsync();

                StatusMessage = $"تم تحميل {Projects.Count} مشروع بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = "فشل في تحميل البيانات";
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadProjectsAsync()
        {
            try
            {
                var projects = await _dataService.GetProjectsSummaryAsync();
                
                Projects.Clear();
                foreach (var project in projects)
                {
                    Projects.Add(project);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل المشاريع: {ex.Message}", ex);
            }
        }

        private async Task LoadReportsAsync()
        {
            try
            {
                var reports = await _dataService.GetAllReportsAsync();
                
                Reports.Clear();
                foreach (var report in reports)
                {
                    Reports.Add(report);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل التقارير: {ex.Message}", ex);
            }
        }

        private async Task LoadFiltersAsync()
        {
            try
            {
                // تحميل المناطق
                var regions = await _dataService.GetAvailableRegionsAsync();
                AvailableRegions.Clear();
                AvailableRegions.Add("الكل");
                foreach (var region in regions)
                {
                    AvailableRegions.Add(region);
                }

                // تحميل الحالات
                var statuses = await _dataService.GetAvailableStatusesAsync();
                AvailableStatuses.Clear();
                AvailableStatuses.Add("الكل");
                foreach (var status in statuses)
                {
                    AvailableStatuses.Add(status);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل فلاتر البحث: {ex.Message}", ex);
            }
        }

        private async Task LoadDatabaseStatsAsync()
        {
            try
            {
                DatabaseStats = await _dataService.GetDatabaseStatisticsAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل إحصائيات قاعدة البيانات: {ex.Message}", ex);
            }
        }

        private async Task SearchAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري البحث...";

                var searchResults = await _dataService.SearchProjectsAsync(
                    SearchCriteria.SearchTerm,
                    SearchCriteria.StatusFilter,
                    SearchCriteria.RegionFilter);

                Projects.Clear();
                foreach (var project in searchResults)
                {
                    Projects.Add(project);
                }

                StatusMessage = $"تم العثور على {Projects.Count} مشروع";
            }
            catch (Exception ex)
            {
                StatusMessage = "فشل في البحث";
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ClearSearch()
        {
            SearchCriteria.Reset();
            _ = Task.Run(LoadProjectsAsync);
        }

        private async Task RefreshDataAsync()
        {
            await LoadDataAsync();
        }

        private async Task DeleteSelectedReportAsync()
        {
            if (SelectedReport == null) return;

            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف التقرير '{SelectedReport.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    IsLoading = true;
                    StatusMessage = "جاري حذف التقرير...";

                    var success = await _dataService.DeleteReportAsync(SelectedReport.Id);
                    
                    if (success)
                    {
                        Reports.Remove(SelectedReport);
                        SelectedReport = null;
                        StatusMessage = "تم حذف التقرير بنجاح";
                        MessageBox.Show("تم حذف التقرير بنجاح", "نجح الحذف", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        StatusMessage = "فشل في حذف التقرير";
                        MessageBox.Show("فشل في حذف التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "حدث خطأ أثناء حذف التقرير";
                MessageBox.Show($"حدث خطأ أثناء حذف التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ViewProjectDetails(object? parameter)
        {
            if (SelectedProject == null) return;

            try
            {
                // فتح نافذة تفاصيل المشروع أو الانتقال إلى صفحة المشاريع
                MessageBox.Show($"عرض تفاصيل المشروع: {SelectedProject.Name}\n" +
                              $"رقم المشروع: {SelectedProject.ProjectNumber}\n" +
                              $"الحالة: {SelectedProject.Status}\n" +
                              $"الميزانية: {SelectedProject.BudgetFormatted}\n" +
                              $"المستفيدين: {SelectedProject.BeneficiariesFormatted}\n" +
                              $"المؤشرات: {SelectedProject.IndicatorsFormatted}\n" +
                              $"التقدم: {SelectedProject.ProgressFormatted}",
                              "تفاصيل المشروع",
                              MessageBoxButton.OK,
                              MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض تفاصيل المشروع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ExportDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تصدير البيانات...";

                // هنا يمكن إضافة منطق التصدير
                await Task.Delay(1000); // محاكاة عملية التصدير

                StatusMessage = "تم تصدير البيانات بنجاح";
                MessageBox.Show("تم تصدير البيانات بنجاح", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = "فشل في تصدير البيانات";
                MessageBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task OnTabChangedAsync(string? tabName)
        {
            if (string.IsNullOrEmpty(tabName)) return;

            SelectedTab = tabName;

            try
            {
                IsLoading = true;

                switch (tabName)
                {
                    case "المشاريع":
                        await LoadProjectsAsync();
                        break;
                    case "التقارير":
                        await LoadReportsAsync();
                        break;
                    case "الإحصائيات":
                        await LoadDatabaseStatsAsync();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات التبويب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        #endregion
    }
}
