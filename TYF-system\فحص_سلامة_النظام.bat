@echo off
chcp 65001 >nul
title فحص سلامة نظام إدارة مؤسسة تمدين شباب

echo ========================================
echo    فحص سلامة نظام إدارة مؤسسة تمدين شباب
echo ========================================
echo.

set "errors=0"

echo Checking essential files...
echo.

REM Check executable file
if exist "TYFManagementSystem\bin\Release\net8.0-windows\TYFManagementSystem.exe" (
    echo [OK] Executable file exists
) else (
    echo [ERROR] Executable file missing
    set /a errors+=1
)

REM Check database folder
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Data" (
    echo [OK] Database folder exists
) else (
    echo [ERROR] Database folder missing
    set /a errors+=1
)

REM فحص قاعدة البيانات
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Data\TYF_Database.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ⚠️  قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً
)

REM فحص ملفات DLL المطلوبة
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Microsoft.EntityFrameworkCore.dll" (
    echo ✅ مكتبات Entity Framework موجودة
) else (
    echo ❌ مكتبات Entity Framework غير موجودة
    set /a errors+=1
)

if exist "TYFManagementSystem\bin\Release\net8.0-windows\Microsoft.Data.Sqlite.dll" (
    echo ✅ مكتبات SQLite موجودة
) else (
    echo ❌ مكتبات SQLite غير موجودة
    set /a errors+=1
)

REM فحص ملف التشغيل
if exist "تشغيل_النظام.bat" (
    echo ✅ ملف التشغيل السريع موجود
) else (
    echo ❌ ملف التشغيل السريع غير موجود
    set /a errors+=1
)

REM فحص ملفات التوثيق
if exist "دليل_التشغيل_السريع.md" (
    echo ✅ دليل التشغيل السريع موجود
) else (
    echo ⚠️  دليل التشغيل السريع غير موجود
)

echo.
echo ========================================

if %errors%==0 (
    echo النتيجة: ✅ النظام سليم وجاهز للتشغيل! 🎉
    echo.
    echo 📍 موقع قاعدة البيانات:
    echo TYFManagementSystem\bin\Release\net8.0-windows\Data\TYF_Database.db
    echo.
    echo 🚀 للتشغيل: انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
    echo 📦 للنقل: انسخ مجلد "TYF-system" بالكامل
) else (
    echo النتيجة: ❌ تم العثور على %errors% مشكلة
    echo يرجى التأكد من اكتمال ملفات النظام
)

echo ========================================
echo.
pause
