<UserControl x:Class="TYFManagementSystem.Views.Reports.ReportsManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:Converters="clr-namespace:TYFManagementSystem.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- تحويل نوع التقرير إلى نص عربي -->
        <Style x:Key="ReportTypeTextStyle" TargetType="TextBlock">
            <Setter Property="Text" Value="{Binding Type}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="IPTT">
                    <Setter Property="Text" Value="IPTT"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Financial">
                    <Setter Property="Text" Value="مالي"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Activities">
                    <Setter Property="Text" Value="أنشطة"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Projects">
                    <Setter Property="Text" Value="مشاريع"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Custom">
                    <Setter Property="Text" Value="مخصص"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2E7D32" Padding="20" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة التقارير" 
                                 FontSize="20" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock Text="إنشاء وإدارة جميع تقارير النظام" 
                                 FontSize="12" 
                                 Foreground="White"
                                 Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="📄 تقرير جديد"
                            Command="{Binding CreateNewReportCommand}"
                            Background="White"
                            Foreground="#2E7D32"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- منطقة البحث والفلاتر -->
        <Border Grid.Row="1" Background="White" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- البحث -->
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="البحث:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 FontSize="12"
                                 Padding="8"
                                 BorderBrush="#ddd"/>
                    </StackPanel>

                    <!-- نوع التقرير -->
                    <StackPanel Grid.Column="1" Margin="0,0,10,0">
                        <TextBlock Text="نوع التقرير:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox SelectedItem="{Binding SelectedReportType}"
                                  ItemsSource="{Binding ReportTypes}"
                                  FontSize="12"
                                  Padding="8"
                                  BorderBrush="#ddd">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock>
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="{Binding}"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding}" Value="IPTT">
                                                        <Setter Property="Text" Value="IPTT"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding}" Value="Financial">
                                                        <Setter Property="Text" Value="مالي"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding}" Value="Activities">
                                                        <Setter Property="Text" Value="أنشطة"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding}" Value="Projects">
                                                        <Setter Property="Text" Value="مشاريع"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding}" Value="Custom">
                                                        <Setter Property="Text" Value="مخصص"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                    </StackPanel>

                    <!-- تاريخ البداية -->
                    <StackPanel Grid.Column="2" Margin="0,0,10,0">
                        <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker SelectedDate="{Binding StartDate}"
                                    FontSize="12"
                                    BorderBrush="#ddd"/>
                    </StackPanel>

                    <!-- تاريخ النهاية -->
                    <StackPanel Grid.Column="3" Margin="0,0,10,0">
                        <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker SelectedDate="{Binding EndDate}"
                                    FontSize="12"
                                    BorderBrush="#ddd"/>
                    </StackPanel>

                    <!-- أزرار الفلاتر -->
                    <StackPanel Grid.Column="4" VerticalAlignment="Bottom">
                        <Button Content="🗑️ مسح الفلاتر"
                                Command="{Binding ClearFiltersCommand}"
                                Background="#FF9800"
                                Foreground="White"
                                Padding="10,8"
                                FontSize="11"
                                BorderThickness="0"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- قائمة التقارير -->
        <Border Grid.Row="2" Background="White" Padding="15">
            <Grid>
                <DataGrid ItemsSource="{Binding FilteredReports}"
                          SelectedItem="{Binding SelectedReport}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          CanUserReorderColumns="True"
                          CanUserSortColumns="True"
                          FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم التقرير" 
                                          Binding="{Binding Name}" 
                                          Width="2*"/>
                        
                        <DataGridTemplateColumn Header="نوع التقرير" Width="*">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Style="{StaticResource ReportTypeTextStyle}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTextColumn Header="تاريخ الإنشاء" 
                                          Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                          Width="*"/>
                        
                        <DataGridTextColumn Header="آخر تعديل" 
                                          Binding="{Binding LastModified, StringFormat=dd/MM/yyyy HH:mm}" 
                                          Width="*"/>
                        
                        <DataGridTextColumn Header="أنشأ بواسطة" 
                                          Binding="{Binding CreatedBy}" 
                                          Width="*"/>
                        
                        <DataGridTextColumn Header="عدد السجلات" 
                                          Binding="{Binding RecordCount}" 
                                          Width="Auto"/>
                        
                        <DataGridTextColumn Header="ملاحظات" 
                                          Binding="{Binding Notes}" 
                                          Width="2*"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- رسالة عدم وجود بيانات -->
                <TextBlock Text="لا توجد تقارير متاحة"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           FontSize="16"
                           Foreground="Gray"
                           Visibility="{Binding FilteredReports.Count, Converter={x:Static Converters:CountToVisibilityConverter.Instance}}"/>
            </Grid>
        </Border>

        <!-- شريط الأدوات -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="15" Margin="0,15,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="👁️ معاينة"
                        Command="{Binding PreviewReportCommand}"
                        Background="#2196F3"
                        Foreground="White"
                        Padding="15,8"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button Content="📤 تصدير"
                        Command="{Binding ExportReportCommand}"
                        Background="#4CAF50"
                        Foreground="White"
                        Padding="15,8"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button Content="🗑️ حذف"
                        Command="{Binding DeleteReportCommand}"
                        Background="#F44336"
                        Foreground="White"
                        Padding="15,8"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button Content="🖨️ طباعة"
                        Background="#795548"
                        Foreground="White"
                        Padding="15,8"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
            </StackPanel>
        </Border>

        <!-- شريط الحالة -->
        <Border Grid.Row="4" Background="#E0E0E0" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"
                           FontSize="11"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding FilteredReports.Count, StringFormat=عدد التقارير: {0}}" 
                               VerticalAlignment="Center"
                               FontSize="11"
                               Margin="10,0"/>
                    
                    <ProgressBar Width="100" 
                                 Height="15"
                                 IsIndeterminate="{Binding IsLoading}"
                                 Visibility="{Binding IsLoading, Converter={x:Static Converters:BoolToVisibilityConverter.Instance}}"
                                 Margin="10,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
