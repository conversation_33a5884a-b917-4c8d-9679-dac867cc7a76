using System.Collections.ObjectModel;
using System.Windows.Input;
using System.IO;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;
using TYFManagementSystem.Services.IPTT;
using TYFManagementSystem.ViewModels;

namespace TYFManagementSystem.ViewModels.IPTT
{
    /// <summary>
    /// ViewModel الرئيسي لنظام IPTT
    /// </summary>
    public class IpttMainViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly IpttDataManager _dataManager;
        private readonly IpttCalculationService _calculationService;
        private readonly IpttLocationService _locationService;
        private readonly IpttValidationService _validationService;
        private readonly IpttExportService _exportService;

        private Project _currentProject;
        private IpttDataModel _dataModel;
        private ObservableCollection<IpttDisplayRow> _displayRows;
        private int _locationCount = 1;
        private bool _isLoading;
        private string _statusMessage = "";
        private bool _hasUnsavedChanges;
        #endregion

        #region Public Properties
        public Project CurrentProject
        {
            get => _currentProject;
            set => SetProperty(ref _currentProject, value);
        }

        public IpttDataModel DataModel
        {
            get => _dataModel;
            set => SetProperty(ref _dataModel, value);
        }

        public ObservableCollection<IpttDisplayRow> DisplayRows
        {
            get => _displayRows;
            set => SetProperty(ref _displayRows, value);
        }

        public int LocationCount
        {
            get => _locationCount;
            set
            {
                if (SetProperty(ref _locationCount, value))
                {
                    OnLocationCountChanged();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set => SetProperty(ref _hasUnsavedChanges, value);
        }

        public Dictionary<int, string> LocationNames { get; private set; }
        #endregion

        #region Commands
        public ICommand LoadDataCommand { get; private set; }
        public ICommand SaveDataCommand { get; private set; }
        public ICommand AddIndicatorCommand { get; private set; }
        public ICommand DeleteIndicatorCommand { get; private set; }
        public ICommand RefreshCalculationsCommand { get; private set; }
        public ICommand ExportToExcelCommand { get; private set; }
        public ICommand ExportToJsonCommand { get; private set; }
        public ICommand CreateBackupCommand { get; private set; }
        public ICommand ValidateDataCommand { get; private set; }
        #endregion

        #region Constructor
        public IpttMainViewModel(Project project)
        {
            _currentProject = project ?? throw new ArgumentNullException(nameof(project));
            
            // تهيئة الخدمات
            _dataManager = new IpttDataManager();
            _calculationService = new IpttCalculationService();
            _locationService = new IpttLocationService();
            _validationService = new IpttValidationService();
            _exportService = new IpttExportService();

            // تهيئة البيانات
            _dataModel = new IpttDataModel { ProjectInfo = project };
            _displayRows = new ObservableCollection<IpttDisplayRow>();
            LocationNames = new Dictionary<int, string>();

            InitializeCommands();
            InitializeAsync();
        }
        #endregion

        #region Command Initialization
        private void InitializeCommands()
        {
            LoadDataCommand = new RelayCommand(async () => await LoadDataAsync());
            SaveDataCommand = new RelayCommand(async () => await SaveDataAsync(), () => HasUnsavedChanges);
            AddIndicatorCommand = new RelayCommand(AddIndicator);
            DeleteIndicatorCommand = new RelayCommand(param => DeleteIndicator(param as string));
            RefreshCalculationsCommand = new RelayCommand(RefreshCalculations);
            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcelAsync());
            ExportToJsonCommand = new RelayCommand(async () => await ExportToJsonAsync());
            CreateBackupCommand = new RelayCommand(async () => await CreateBackupAsync());
            ValidateDataCommand = new RelayCommand(ValidateData);
        }
        #endregion

        #region Initialization
        private async void InitializeAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تهيئة البيانات...";

                // تحميل البيانات إن وجدت
                await LoadDataAsync();

                // إنشاء بيانات فارغة إذا لم توجد بيانات محفوظة
                if (DataModel.Indicators.Count == 0)
                {
                    InitializeEmptyData();
                }

                StatusMessage = "تم تحميل البيانات بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التهيئة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void InitializeEmptyData()
        {
            // إنشاء أعمدة الأشهر
            DataModel.MonthColumns = _dataManager.GenerateMonthColumns(
                CurrentProject.StartDate, CurrentProject.EndDate);

            // إنشاء بيانات المواقع الفارغة
            DataModel.LocationData = _locationService.CreateLocationData(
                DisplayRows, DataModel.MonthColumns, LocationCount);

            // تحديث أسماء المواقع
            LocationNames = _locationService.GetLocationNames(LocationCount);

            HasUnsavedChanges = false;
        }
        #endregion

        #region Data Operations
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل البيانات...";

                var loadedData = await _dataManager.LoadProjectDataAsync(CurrentProject.Id);
                if (loadedData != null)
                {
                    DataModel = loadedData;
                    LocationCount = loadedData.LocationCount;
                    
                    // تحويل المؤشرات إلى صفوف عرض
                    DisplayRows = _dataManager.ConvertIndicatorsToDisplayRows(
                        DataModel.Indicators, DataModel.MonthColumns);

                    // تحديث أسماء المواقع
                    LocationNames = _locationService.GetLocationNames(LocationCount);

                    HasUnsavedChanges = false;
                    StatusMessage = "تم تحميل البيانات بنجاح";
                }
                else
                {
                    StatusMessage = "لا توجد بيانات محفوظة للمشروع";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SaveDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري حفظ البيانات...";

                // التحقق من صحة البيانات قبل الحفظ
                var validation = _validationService.ValidateCompleteIpttData(DataModel);
                if (!validation.IsValid)
                {
                    StatusMessage = $"خطأ في البيانات: {string.Join(", ", validation.Errors)}";
                    return;
                }

                // تحديث البيانات الأساسية من صفوف العرض
                _dataManager.UpdateIndicatorsFromDisplayRows(DataModel.Indicators, DisplayRows);

                // حفظ البيانات
                bool success = await _dataManager.SaveProjectDataAsync(DataModel);
                if (success)
                {
                    HasUnsavedChanges = false;
                    StatusMessage = "تم حفظ البيانات بنجاح";

                    // إنشاء نسخة احتياطية
                    await CreateBackupAsync();
                }
                else
                {
                    StatusMessage = "فشل في حفظ البيانات";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في حفظ البيانات: {ex.Message}";
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }
        #endregion

        #region Indicator Management
        private void AddIndicator()
        {
            try
            {
                // هذه الوظيفة ستفتح حوار إضافة مؤشر جديد
                // سيتم تطبيقها في الواجهة
                HasUnsavedChanges = true;
                StatusMessage = "تم إضافة مؤشر جديد";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إضافة المؤشر: {ex.Message}";
            }
        }

        private void DeleteIndicator(string indicatorId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(indicatorId))
                    return;

                // حذف المؤشر من البيانات الأساسية
                var indicatorToRemove = DataModel.Indicators.FirstOrDefault(i => i.No == indicatorId);
                if (indicatorToRemove != null)
                {
                    DataModel.Indicators.Remove(indicatorToRemove);
                }

                // حذف الصفوف المرتبطة من العرض
                var rowsToRemove = DisplayRows.Where(r => r.IndicatorId == indicatorId).ToList();
                foreach (var row in rowsToRemove)
                {
                    DisplayRows.Remove(row);
                }

                // حذف من بيانات المواقع
                foreach (var locationRows in DataModel.LocationData.Values)
                {
                    var locationRowsToRemove = locationRows.Where(r => r.IndicatorId == indicatorId).ToList();
                    foreach (var row in locationRowsToRemove)
                    {
                        locationRows.Remove(row);
                    }
                }

                HasUnsavedChanges = true;
                StatusMessage = "تم حذف المؤشر";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في حذف المؤشر: {ex.Message}";
            }
        }
        #endregion

        #region Calculations
        private void RefreshCalculations()
        {
            try
            {
                StatusMessage = "جاري تحديث الحسابات...";

                _locationService.RefreshAllLocationCalculations(
                    DataModel.LocationData, DataModel.MonthColumns);

                StatusMessage = "تم تحديث الحسابات بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحديث الحسابات: {ex.Message}";
            }
        }
        #endregion

        #region Export Operations
        private async Task ExportToExcelAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تصدير البيانات إلى Excel...";

                var fileName = $"IPTT_{CurrentProject.Name}_{DateTime.Now:yyyy-MM-dd}.xlsx";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                bool success = await _exportService.ExportToExcelAsync(DataModel, filePath);
                if (success)
                {
                    StatusMessage = $"تم تصدير البيانات إلى: {filePath}";
                }
                else
                {
                    StatusMessage = "فشل في تصدير البيانات";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التصدير: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ExportToJsonAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تصدير البيانات إلى JSON...";

                var fileName = $"IPTT_{CurrentProject.Name}_{DateTime.Now:yyyy-MM-dd}.json";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                bool success = await _exportService.ExportToJsonAsync(DataModel, filePath);
                if (success)
                {
                    StatusMessage = $"تم تصدير البيانات إلى: {filePath}";
                }
                else
                {
                    StatusMessage = "فشل في تصدير البيانات";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التصدير: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task CreateBackupAsync()
        {
            try
            {
                var backupPath = await _exportService.CreateBackupAsync(DataModel);
                // لا نعرض رسالة للمستخدم لأن النسخة الاحتياطية تلقائية
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ فقط دون إزعاج المستخدم
                System.Diagnostics.Debug.WriteLine($"فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }
        #endregion

        #region Validation
        private void ValidateData()
        {
            try
            {
                var validation = _validationService.ValidateCompleteIpttData(DataModel);
                
                if (validation.IsValid)
                {
                    StatusMessage = "البيانات صحيحة";
                }
                else
                {
                    StatusMessage = $"أخطاء في البيانات: {validation.Errors.Count}, تحذيرات: {validation.Warnings.Count}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التحقق من البيانات: {ex.Message}";
            }
        }
        #endregion

        #region Event Handlers
        private void OnLocationCountChanged()
        {
            try
            {
                // التحقق من صحة عدد المواقع
                var validation = _validationService.ValidateLocationCount(LocationCount);
                if (!validation.IsValid)
                {
                    StatusMessage = string.Join(", ", validation.Errors);
                    return;
                }

                // التحقق من وجود مؤشرات
                if (DisplayRows == null || DisplayRows.Count == 0)
                {
                    StatusMessage = "يرجى إدخال المؤشرات أولاً قبل تحديد عدد المواقع!";
                    return;
                }

                // حفظ البيانات الحالية قبل إعادة الإنشاء
                var existingLocationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>();
                if (DataModel.LocationData != null)
                {
                    foreach (var kvp in DataModel.LocationData)
                    {
                        existingLocationData[kvp.Key] = new ObservableCollection<IpttDisplayRow>(kvp.Value);
                    }
                }

                // إعادة إنشاء بيانات المواقع مع الحفاظ على البيانات الموجودة
                DataModel.LocationData = _locationService.CreateLocationData(
                    DisplayRows, DataModel.MonthColumns, LocationCount);

                // استعادة البيانات المحفوظة للمواقع الموجودة
                foreach (var kvp in existingLocationData)
                {
                    int locationId = kvp.Key;
                    var savedData = kvp.Value;

                    if (DataModel.LocationData.ContainsKey(locationId))
                    {
                        var currentData = DataModel.LocationData[locationId];

                        // نسخ البيانات المحفوظة إلى البيانات الجديدة
                        for (int i = 0; i < Math.Min(savedData.Count, currentData.Count); i++)
                        {
                            var savedRow = savedData[i];
                            var currentRow = currentData[i];

                            // نسخ البيانات المدخلة
                            currentRow.Target = savedRow.Target;
                            currentRow.Achievement = savedRow.Achievement;
                            currentRow.AchievementPercentage = savedRow.AchievementPercentage;

                            // نسخ البيانات الشهرية
                            foreach (var monthData in savedRow.MonthlyData)
                            {
                                if (currentRow.MonthlyData.ContainsKey(monthData.Key))
                                {
                                    currentRow.MonthlyData[monthData.Key] = monthData.Value;
                                }
                            }
                        }
                    }
                }

                // تحديث أسماء المواقع
                LocationNames = _locationService.GetLocationNames(LocationCount);

                // تحديث عدد المواقع في النموذج
                DataModel.LocationCount = LocationCount;

                HasUnsavedChanges = true;
                StatusMessage = $"تم تحديث عدد المواقع إلى {LocationCount} مع الحفاظ على البيانات المدخلة";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحديث المواقع: {ex.Message}";
            }
        }
        #endregion

        #region Cleanup
        public void Dispose()
        {
            _dataManager?.Dispose();
        }
        #endregion
    }
}
