using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Models.Reports;
using TYFManagementSystem.Services.Reports;
using TYFManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace TYFManagementSystem.Views.Reports
{
    /// <summary>
    /// Interaction logic for CreateReportDialog.xaml
    /// </summary>
    public partial class CreateReportDialog : Window
    {
        private readonly ReportService _reportService;
        private ReportGenerationResult? _lastResult;

        public Report? CreatedReport { get; private set; }

        public CreateReportDialog()
        {
            InitializeComponent();
            _reportService = new ReportService();
            LoadProjects();
            SetupEventHandlers();
        }

        private async void LoadProjects()
        {
            try
            {
                using var context = new TyfDbContext();
                var projects = await context.IpttProjects
                    .Select(p => new { p.ProjectId, p.ProjectName })
                    .ToListAsync();

                ProjectsListBox.Items.Clear();
                foreach (var project in projects)
                {
                    var checkBox = new CheckBox
                    {
                        Content = project.ProjectName,
                        Tag = project.ProjectId,
                        Margin = new Thickness(0, 2, 0, 2)
                    };
                    ProjectsListBox.Items.Add(checkBox);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المشاريع: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetupEventHandlers()
        {
            AllProjectsCheckBox.Checked += (s, e) => ProjectsListBox.IsEnabled = false;
            AllProjectsCheckBox.Unchecked += (s, e) => ProjectsListBox.IsEnabled = true;
            
            AllLocationsCheckBox.Checked += (s, e) => LocationsPanel.IsEnabled = false;
            AllLocationsCheckBox.Unchecked += (s, e) => LocationsPanel.IsEnabled = true;
        }

        private async void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var criteria = CreateReportCriteria();
                
                GenerateButton.IsEnabled = false;
                GenerateButton.Content = "⏳ جاري الإنشاء...";

                _lastResult = await _reportService.GenerateIpttReportAsync(criteria);

                if (_lastResult.Success && _lastResult.Report != null)
                {
                    var saved = await _reportService.SaveReportAsync(_lastResult.Report);
                    if (saved)
                    {
                        CreatedReport = _lastResult.Report;
                        MessageBox.Show($"تم إنشاء التقرير بنجاح!\nعدد السجلات: {_lastResult.RecordCount}\nوقت الإنشاء: {_lastResult.GenerationTime.TotalSeconds:F1} ثانية", 
                            "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        MessageBox.Show("تم إنشاء التقرير ولكن فشل في حفظه", "تحذير", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show($"فشل في إنشاء التقرير: {_lastResult.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                GenerateButton.IsEnabled = true;
                GenerateButton.Content = "🔄 إنشاء التقرير";
            }
        }

        private async void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var criteria = CreateReportCriteria();
                
                PreviewButton.IsEnabled = false;
                PreviewButton.Content = "⏳ جاري المعاينة...";

                var result = await _reportService.GenerateIpttReportAsync(criteria);

                if (result.Success && result.Data.Any())
                {
                    var previewWindow = new ReportPreviewDialog(result);
                    previewWindow.Owner = this;
                    previewWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show($"فشل في إنشاء المعاينة: {result.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                PreviewButton.IsEnabled = true;
                PreviewButton.Content = "👁️ معاينة";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(ReportNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم التقرير", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                ReportNameTextBox.Focus();
                return false;
            }

            if (ReportTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع التقرير", "تحذير", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                ReportTypeComboBox.Focus();
                return false;
            }

            return true;
        }

        private ReportCriteria CreateReportCriteria()
        {
            var criteria = new ReportCriteria
            {
                Name = ReportNameTextBox.Text.Trim(),
                Description = DescriptionTextBox.Text.Trim(),
                StartDate = StartDatePicker.SelectedDate,
                EndDate = EndDatePicker.SelectedDate,
                IncludeSummary = IncludeSummaryCheckBox.IsChecked == true,
                IncludeCharts = IncludeChartsCheckBox.IsChecked == true,
                GroupBy = GroupByProjectCheckBox.IsChecked == true ? "Project" : ""
            };

            // تحديد نوع التقرير
            if (ReportTypeComboBox.SelectedItem is ComboBoxItem selectedType)
            {
                if (Enum.TryParse<ReportType>(selectedType.Tag.ToString(), out var reportType))
                {
                    criteria.Type = reportType;
                }
            }

            // تحديد المشاريع
            if (AllProjectsCheckBox.IsChecked != true)
            {
                foreach (CheckBox item in ProjectsListBox.Items)
                {
                    if (item.IsChecked == true && item.Tag != null)
                    {
                        criteria.ProjectIds.Add(item.Tag.ToString()!);
                    }
                }
            }

            // تحديد المواقع
            if (AllLocationsCheckBox.IsChecked != true)
            {
                foreach (CheckBox item in LocationsPanel.Children.OfType<CheckBox>())
                {
                    if (item.IsChecked == true)
                    {
                        // استخراج رقم الموقع من النص
                        var locationText = item.Content.ToString();
                        if (locationText != null && locationText.Contains(" "))
                        {
                            var parts = locationText.Split(' ');
                            if (parts.Length > 1 && int.TryParse(parts[1], out int locationId))
                            {
                                criteria.LocationIds.Add(locationId);
                            }
                        }
                    }
                }
            }

            return criteria;
        }

        protected override void OnClosed(EventArgs e)
        {
            _reportService?.Dispose();
            base.OnClosed(e);
        }
    }
}
