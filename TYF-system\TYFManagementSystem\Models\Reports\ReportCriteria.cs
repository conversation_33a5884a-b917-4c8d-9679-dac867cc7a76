using System;
using System.Collections.Generic;

namespace TYFManagementSystem.Models.Reports
{
    /// <summary>
    /// معايير إنشاء التقرير
    /// </summary>
    public class ReportCriteria
    {
        public string Name { get; set; } = "";
        public ReportType Type { get; set; }
        public string Description { get; set; } = "";
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<string> ProjectIds { get; set; } = new();
        public List<int> LocationIds { get; set; } = new();
        public List<string> IndicatorIds { get; set; } = new();
        public Dictionary<string, object> CustomFilters { get; set; } = new();
        public bool IncludeCharts { get; set; } = false;
        public bool IncludeSummary { get; set; } = true;
        public string GroupBy { get; set; } = "";
        public string SortBy { get; set; } = "";
        public bool SortDescending { get; set; } = false;
    }

    /// <summary>
    /// نتيجة إنشاء التقرير
    /// </summary>
    public class ReportGenerationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public Report? Report { get; set; }
        public List<ReportDataRow> Data { get; set; } = new();
        public ReportSummary Summary { get; set; } = new();
        public TimeSpan GenerationTime { get; set; }
        public int RecordCount { get; set; }
    }

    /// <summary>
    /// صف بيانات التقرير
    /// </summary>
    public class ReportDataRow
    {
        public Dictionary<string, object> Values { get; set; } = new();
        public string RowType { get; set; } = "Data"; // Data, Header, Summary, etc.
        public int Level { get; set; } = 0; // للتجميع الهرمي
    }

    /// <summary>
    /// ملخص التقرير
    /// </summary>
    public class ReportSummary
    {
        public int TotalRecords { get; set; }
        public Dictionary<string, object> Totals { get; set; } = new();
        public Dictionary<string, object> Averages { get; set; } = new();
        public Dictionary<string, object> Statistics { get; set; } = new();
        public List<string> Notes { get; set; } = new();
    }

    /// <summary>
    /// إعدادات التصدير
    /// </summary>
    public class ExportSettings
    {
        public ExportFormat Format { get; set; }
        public string FileName { get; set; } = "";
        public bool IncludeHeaders { get; set; } = true;
        public bool IncludeSummary { get; set; } = true;
        public bool IncludeCharts { get; set; } = false;
        public string Template { get; set; } = "";
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }
}
