@echo off
title TYF System Health Check

echo ========================================
echo    TYF Management System Health Check
echo ========================================
echo.

set "errors=0"

echo Checking system files...
echo.

REM Check executable
if exist "TYFManagementSystem\bin\Release\net8.0-windows\TYFManagementSystem.exe" (
    echo [OK] Executable file exists
) else (
    echo [ERROR] Executable file missing
    set /a errors+=1
)

REM Check database folder
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Data" (
    echo [OK] Database folder exists
) else (
    echo [ERROR] Database folder missing
    set /a errors+=1
)

REM Check database file
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Data\TYF_Database.db" (
    echo [OK] Database file exists
) else (
    echo [WARNING] Database file missing - will be created automatically
)

REM Check Entity Framework
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Microsoft.EntityFrameworkCore.dll" (
    echo [OK] Entity Framework libraries found
) else (
    echo [ERROR] Entity Framework libraries missing
    set /a errors+=1
)

REM Check SQLite
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Microsoft.Data.Sqlite.dll" (
    echo [OK] SQLite libraries found
) else (
    echo [ERROR] SQLite libraries missing
    set /a errors+=1
)

echo.
echo ========================================

if %errors%==0 (
    echo Result: [SUCCESS] System is healthy and ready! 
    echo.
    echo Database location:
    echo TYFManagementSystem\bin\Release\net8.0-windows\Data\TYF_Database.db
    echo.
    echo To run: Double-click "تشغيل_النظام.bat"
    echo To move: Copy entire "TYF-system" folder
) else (
    echo Result: [ERROR] Found %errors% issues
    echo Please ensure all system files are complete
)

echo ========================================
echo.
pause
