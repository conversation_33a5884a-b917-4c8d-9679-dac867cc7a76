<Window x:Class="TYFManagementSystem.Views.Reports.ExportReportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تصدير التقرير"
        Width="500" Height="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2E7D32" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📤" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <StackPanel>
                    <TextBlock Text="تصدير التقرير" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="White"/>
                    <TextBlock x:Name="ReportNameTextBlock"
                             Text="اختر صيغة التصدير المناسبة" 
                             FontSize="12" 
                             Foreground="White"
                             Opacity="0.9"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- معلومات التقرير -->
                <GroupBox Header="معلومات التقرير" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم التقرير:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="ReportTitleTextBlock" Text="" FontWeight="Bold" Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="عدد السجلات:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="RecordCountTextBlock" Text="0" Margin="0,0,0,5"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="الحجم المتوقع:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="EstimatedSizeTextBlock" Text="غير محدد" Margin="0,0,0,5"/>
                    </Grid>
                </GroupBox>

                <!-- صيغة التصدير -->
                <GroupBox Header="صيغة التصدير" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <RadioButton x:Name="ExcelRadioButton" 
                                   Content="📊 Excel (.xlsx) - الأفضل للتحليل والمعالجة" 
                                   IsChecked="True" 
                                   Margin="0,5"
                                   FontSize="12"/>
                        
                        <RadioButton x:Name="CsvRadioButton" 
                                   Content="📄 CSV (.csv) - متوافق مع جميع البرامج" 
                                   Margin="0,5"
                                   FontSize="12"/>
                        
                        <RadioButton x:Name="JsonRadioButton" 
                                   Content="🔧 JSON (.json) - للتطبيقات والبرمجة" 
                                   Margin="0,5"
                                   FontSize="12"/>
                        
                        <RadioButton x:Name="XmlRadioButton" 
                                   Content="📋 XML (.xml) - للتبادل مع الأنظمة الأخرى" 
                                   Margin="0,5"
                                   FontSize="12"/>
                        
                        <RadioButton x:Name="PdfRadioButton" 
                                   Content="📑 PDF (HTML) - للطباعة والعرض" 
                                   Margin="0,5"
                                   FontSize="12"/>
                    </StackPanel>
                </GroupBox>

                <!-- خيارات إضافية -->
                <GroupBox Header="خيارات إضافية" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <CheckBox x:Name="IncludeHeadersCheckBox" 
                                Content="تضمين رؤوس الأعمدة" 
                                IsChecked="True" 
                                Margin="0,5"/>
                        
                        <CheckBox x:Name="IncludeReportInfoCheckBox" 
                                Content="تضمين معلومات التقرير" 
                                IsChecked="True" 
                                Margin="0,5"/>
                        
                        <CheckBox x:Name="OpenAfterExportCheckBox" 
                                Content="فتح الملف بعد التصدير" 
                                IsChecked="True" 
                                Margin="0,5"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <TextBlock Text="ترميز الملف:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox x:Name="EncodingComboBox" Width="120" SelectedIndex="0">
                                <ComboBoxItem Content="UTF-8" Tag="UTF-8"/>
                                <ComboBoxItem Content="Windows-1256" Tag="Windows-1256"/>
                                <ComboBoxItem Content="ASCII" Tag="ASCII"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- معاينة اسم الملف -->
                <GroupBox Header="معاينة اسم الملف" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <TextBlock Text="اسم الملف المقترح:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="FileNamePreviewTextBox" 
                               IsReadOnly="True" 
                               Background="#F5F5F5"
                               Padding="5"
                               FontFamily="Consolas"/>
                        <TextBlock Text="يمكنك تغيير اسم الملف عند الحفظ" 
                                 FontSize="10" 
                                 Foreground="Gray" 
                                 Margin="0,5,0,0"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="ExportButton" 
                        Content="📤 تصدير الآن"
                        Click="ExportButton_Click"
                        Background="#4CAF50"
                        Foreground="White"
                        Padding="20,10"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button x:Name="PreviewButton" 
                        Content="👁️ معاينة"
                        Click="PreviewButton_Click"
                        Background="#2196F3"
                        Foreground="White"
                        Padding="20,10"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button x:Name="CancelButton" 
                        Content="❌ إلغاء"
                        Click="CancelButton_Click"
                        Background="#757575"
                        Foreground="White"
                        Padding="20,10"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
