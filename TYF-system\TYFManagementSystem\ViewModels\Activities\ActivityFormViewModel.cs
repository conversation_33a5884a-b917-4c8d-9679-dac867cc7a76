using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.Activities;
using TYFManagementSystem.Services.Activities;

namespace TYFManagementSystem.ViewModels.Activities
{
    /// <summary>
    /// ViewModel لنافذة إنشاء/تعديل النشاط
    /// </summary>
    public class ActivityFormViewModel : BaseViewModel, IDisposable
    {
        #region Private Fields
        private readonly ActivityService _activityService;
        private FieldActivity _activity;
        private bool _isEditMode;
        private bool _isLoading;
        private string _statusMessage = "";
        private ObservableCollection<string> _availableProjects;
        private ObservableCollection<string> _availableIndicators;
        private ObservableCollection<ActivityIndicatorLink> _linkedIndicators;
        #endregion

        #region Properties
        public FieldActivity Activity
        {
            get => _activity;
            set => SetProperty(ref _activity, value);
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public ObservableCollection<string> AvailableProjects
        {
            get => _availableProjects;
            set => SetProperty(ref _availableProjects, value);
        }

        public ObservableCollection<string> AvailableIndicators
        {
            get => _availableIndicators;
            set => SetProperty(ref _availableIndicators, value);
        }

        public ObservableCollection<ActivityIndicatorLink> LinkedIndicators
        {
            get => _linkedIndicators;
            set => SetProperty(ref _linkedIndicators, value);
        }

        // خصائص النشاط للربط المباشر
        public string ActivityName
        {
            get => Activity.ActivityName;
            set
            {
                Activity.ActivityName = value;
                OnPropertyChanged();
            }
        }

        public string Description
        {
            get => Activity.Description;
            set
            {
                Activity.Description = value;
                OnPropertyChanged();
            }
        }

        public string Objectives
        {
            get => Activity.Objectives;
            set
            {
                Activity.Objectives = value;
                OnPropertyChanged();
            }
        }

        public string ProjectId
        {
            get => Activity.ProjectId;
            set
            {
                Activity.ProjectId = value;
                OnPropertyChanged();
                _ = LoadProjectIndicatorsAsync();
            }
        }

        public string Location
        {
            get => Activity.Location;
            set
            {
                Activity.Location = value;
                OnPropertyChanged();
            }
        }

        public string LocationDetails
        {
            get => Activity.LocationDetails;
            set
            {
                Activity.LocationDetails = value;
                OnPropertyChanged();
            }
        }

        public DateTime StartDate
        {
            get => Activity.StartDate;
            set
            {
                Activity.StartDate = value;
                OnPropertyChanged();
                ValidateDates();
            }
        }

        public DateTime EndDate
        {
            get => Activity.EndDate;
            set
            {
                Activity.EndDate = value;
                OnPropertyChanged();
                ValidateDates();
            }
        }

        public ActivityStatus Status
        {
            get => Activity.Status;
            set
            {
                Activity.Status = value;
                OnPropertyChanged();
            }
        }

        public ActivityPriority Priority
        {
            get => Activity.Priority;
            set
            {
                Activity.Priority = value;
                OnPropertyChanged();
            }
        }

        public string ResponsiblePerson
        {
            get => Activity.ResponsiblePerson;
            set
            {
                Activity.ResponsiblePerson = value;
                OnPropertyChanged();
            }
        }

        public string ContactInfo
        {
            get => Activity.ContactInfo;
            set
            {
                Activity.ContactInfo = value;
                OnPropertyChanged();
            }
        }

        public int EstimatedParticipants
        {
            get => Activity.EstimatedParticipants;
            set
            {
                Activity.EstimatedParticipants = value;
                OnPropertyChanged();
            }
        }

        public decimal EstimatedBudget
        {
            get => Activity.EstimatedBudget;
            set
            {
                Activity.EstimatedBudget = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => Activity.Notes;
            set
            {
                Activity.Notes = value;
                OnPropertyChanged();
            }
        }

        // خيارات الحالة والأولوية
        public Array ActivityStatuses => Enum.GetValues(typeof(ActivityStatus));
        public Array ActivityPriorities => Enum.GetValues(typeof(ActivityPriority));

        public string WindowTitle => IsEditMode ? "تعديل النشاط" : "إنشاء نشاط جديد";
        public string SaveButtonText => IsEditMode ? "حفظ التغييرات" : "إنشاء النشاط";
        #endregion

        #region Commands
        public ICommand SaveCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand AddIndicatorLinkCommand { get; private set; }
        public ICommand RemoveIndicatorLinkCommand { get; private set; }
        public ICommand SelectLocationOnMapCommand { get; private set; }
        public ICommand UploadDocumentCommand { get; private set; }
        #endregion

        #region Constructor
        public ActivityFormViewModel(string? activityId = null)
        {
            _activityService = new ActivityService();
            _activity = new FieldActivity();
            _availableProjects = new ObservableCollection<string>();
            _availableIndicators = new ObservableCollection<string>();
            _linkedIndicators = new ObservableCollection<ActivityIndicatorLink>();

            InitializeCommands();
            _ = InitializeAsync(activityId);
        }
        #endregion

        #region Private Methods
        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(async () => await SaveActivityAsync(), CanSave);
            CancelCommand = new RelayCommand(() => { /* سيتم تنفيذ إغلاق النافذة */ });
            AddIndicatorLinkCommand = new RelayCommand(async () => await AddIndicatorLinkAsync());
            RemoveIndicatorLinkCommand = new RelayCommand(async () => await RemoveIndicatorLinkAsync(null));
            SelectLocationOnMapCommand = new RelayCommand(async () => await SelectLocationOnMapAsync());
            UploadDocumentCommand = new RelayCommand(async () => await UploadDocumentAsync());
        }

        private async Task InitializeAsync(string? activityId)
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل البيانات...";

                // تحميل المشاريع المتاحة
                await LoadAvailableProjectsAsync();

                if (!string.IsNullOrEmpty(activityId))
                {
                    // وضع التعديل
                    IsEditMode = true;
                    var activity = await _activityService.GetActivityAsync(activityId);
                    if (activity != null)
                    {
                        Activity = activity;
                        LinkedIndicators.Clear();
                        foreach (var link in activity.LinkedIndicators)
                        {
                            LinkedIndicators.Add(link);
                        }
                        await LoadProjectIndicatorsAsync();
                    }
                }
                else
                {
                    // وضع الإنشاء
                    IsEditMode = false;
                    Activity = new FieldActivity
                    {
                        StartDate = DateTime.Today,
                        EndDate = DateTime.Today.AddDays(1),
                        Status = ActivityStatus.Planned,
                        Priority = ActivityPriority.Medium
                    };
                }

                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadAvailableProjectsAsync()
        {
            try
            {
                // سيتم تحميل المشاريع من قاعدة البيانات
                AvailableProjects.Clear();
                AvailableProjects.Add("مشروع 1");
                AvailableProjects.Add("مشروع 2");
                AvailableProjects.Add("مشروع 3");
                
                await Task.Delay(100); // محاكاة تحميل البيانات
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل المشاريع: {ex.Message}";
            }
        }

        private async Task LoadProjectIndicatorsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(ProjectId)) return;

                AvailableIndicators.Clear();
                // سيتم تحميل المؤشرات من قاعدة البيانات حسب المشروع
                AvailableIndicators.Add("مؤشر 1");
                AvailableIndicators.Add("مؤشر 2");
                AvailableIndicators.Add("مؤشر 3");
                
                await Task.Delay(100); // محاكاة تحميل البيانات
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل المؤشرات: {ex.Message}";
            }
        }

        private async Task SaveActivityAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = IsEditMode ? "جاري حفظ التغييرات..." : "جاري إنشاء النشاط...";

                bool success;
                if (IsEditMode)
                {
                    success = await _activityService.UpdateActivityAsync(Activity);
                }
                else
                {
                    success = await _activityService.CreateActivityAsync(Activity);
                }

                if (success)
                {
                    StatusMessage = IsEditMode ? "تم حفظ التغييرات بنجاح" : "تم إنشاء النشاط بنجاح";
                    // سيتم إغلاق النافذة
                }
                else
                {
                    StatusMessage = IsEditMode ? "فشل في حفظ التغييرات" : "فشل في إنشاء النشاط";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في الحفظ: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSave()
        {
            return !string.IsNullOrWhiteSpace(ActivityName) &&
                   !string.IsNullOrWhiteSpace(ProjectId) &&
                   !string.IsNullOrWhiteSpace(Location) &&
                   !string.IsNullOrWhiteSpace(ResponsiblePerson) &&
                   StartDate <= EndDate;
        }

        private async Task AddIndicatorLinkAsync()
        {
            try
            {
                StatusMessage = "إضافة ربط مؤشر...";
                // سيتم فتح نافذة اختيار المؤشر
                await Task.Delay(100);
                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إضافة الربط: {ex.Message}";
            }
        }

        private async Task RemoveIndicatorLinkAsync(ActivityIndicatorLink? link)
        {
            if (link == null) return;

            try
            {
                LinkedIndicators.Remove(link);
                StatusMessage = "تم إزالة ربط المؤشر";
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إزالة الربط: {ex.Message}";
            }
        }

        private async Task SelectLocationOnMapAsync()
        {
            try
            {
                StatusMessage = "فتح الخريطة لاختيار الموقع...";
                // سيتم فتح نافذة الخريطة
                await Task.Delay(100);
                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في فتح الخريطة: {ex.Message}";
            }
        }

        private async Task UploadDocumentAsync()
        {
            try
            {
                StatusMessage = "رفع مستند...";
                // سيتم فتح نافذة اختيار الملف
                await Task.Delay(100);
                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في رفع المستند: {ex.Message}";
            }
        }

        private void ValidateDates()
        {
            if (StartDate > EndDate)
            {
                StatusMessage = "تاريخ البداية يجب أن يكون قبل تاريخ النهاية";
            }
            else
            {
                StatusMessage = "جاهز";
            }
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            _activityService?.Dispose();
        }
        #endregion
    }
}
