<Window x:Class="TYFManagementSystem.Views.Reports.ReportPreviewDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="معاينة التقرير"
        Width="900" Height="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2E7D32" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="👁️" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock x:Name="ReportTitleTextBlock" 
                                 Text="معاينة التقرير" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock x:Name="ReportInfoTextBlock"
                                 Text="معلومات التقرير" 
                                 FontSize="12" 
                                 Foreground="White"
                                 Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="ExportButton"
                            Content="📤 تصدير"
                            Click="ExportButton_Click"
                            Background="White"
                            Foreground="#2E7D32"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button x:Name="PrintButton"
                            Content="🖨️ طباعة"
                            Click="PrintButton_Click"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- محتوى التقرير -->
        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="📊 البيانات">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- معلومات سريعة -->
                    <Border Grid.Row="0" Background="#F5F5F5" Padding="15" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="عدد السجلات" FontWeight="Bold" FontSize="12"/>
                                <TextBlock x:Name="RecordCountTextBlock" Text="0" FontSize="16" Foreground="#2E7D32"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="وقت الإنشاء" FontWeight="Bold" FontSize="12"/>
                                <TextBlock x:Name="GenerationTimeTextBlock" Text="0 ثانية" FontSize="16" Foreground="#2E7D32"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="نوع التقرير" FontWeight="Bold" FontSize="12"/>
                                <TextBlock x:Name="ReportTypeTextBlock" Text="IPTT" FontSize="16" Foreground="#2E7D32"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3">
                                <TextBlock Text="الحالة" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Text="جاهز للعرض" FontSize="16" Foreground="#4CAF50"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- جدول البيانات -->
                    <DataGrid Grid.Row="1" 
                              x:Name="ReportDataGrid"
                              AutoGenerateColumns="True"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              SelectionMode="Extended"
                              CanUserReorderColumns="True"
                              CanUserSortColumns="True"
                              FontSize="11"
                              AlternatingRowBackground="#F9F9F9">
                        
                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="#2E7D32"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Padding" Value="8"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>
                    </DataGrid>
                </Grid>
            </TabItem>

            <TabItem Header="📈 الملخص">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <TextBlock Text="ملخص التقرير" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Border Background="#E8F5E8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="📊 إحصائيات عامة" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <StackPanel Grid.Column="0">
                                        <TextBlock x:Name="SummaryText1" Text="• إجمالي المؤشرات: 0" Margin="0,2"/>
                                        <TextBlock x:Name="SummaryText2" Text="• إجمالي المشاريع: 0" Margin="0,2"/>
                                    </StackPanel>
                                    
                                    <StackPanel Grid.Column="1">
                                        <TextBlock x:Name="SummaryText3" Text="• إجمالي أنواع البيانات: 0" Margin="0,2"/>
                                        <TextBlock x:Name="SummaryText4" Text="• متوسط الإنجاز: 0%" Margin="0,2"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <Border Background="#FFF3E0" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="📋 تفاصيل إضافية" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                                <TextBlock x:Name="AdditionalDetailsTextBlock" 
                                         Text="لا توجد تفاصيل إضافية متاحة"
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </Border>

                        <Border Background="#E3F2FD" Padding="15">
                            <StackPanel>
                                <TextBlock Text="📝 ملاحظات" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                                <TextBlock Text="• هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة مؤسسة تمدين شباب" Margin="0,2"/>
                                <TextBlock x:Name="GenerationDateTextBlock" Text="• تاريخ الإنشاء: " Margin="0,2"/>
                                <TextBlock Text="• يمكن تصدير هذا التقرير بصيغ مختلفة (PDF، Excel، CSV)" Margin="0,2"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#E0E0E0" Padding="15,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           x:Name="StatusTextBlock"
                           Text="جاهز للعرض" 
                           VerticalAlignment="Center"
                           FontSize="11"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SaveButton"
                            Content="💾 حفظ التقرير"
                            Click="SaveButton_Click"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,5"
                            FontSize="11"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="❌ إغلاق"
                            Click="CloseButton_Click"
                            Background="#757575"
                            Foreground="White"
                            Padding="15,5"
                            FontSize="11"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
