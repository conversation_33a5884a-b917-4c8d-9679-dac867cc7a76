<Window x:Class="TYFManagementSystem.Views.Reports.CreateReportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنشاء تقرير جديد"
        Width="600" Height="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#2E7D32" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📄" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <StackPanel>
                    <TextBlock Text="إنشاء تقرير جديد" 
                             FontSize="18" 
                             FontWeight="Bold" 
                             Foreground="White"/>
                    <TextBlock Text="اختر نوع التقرير والمعايير المطلوبة" 
                             FontSize="12" 
                             Foreground="White"
                             Opacity="0.9"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- معلومات أساسية -->
                <GroupBox Header="معلومات التقرير الأساسية" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم التقرير:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="ReportNameTextBox" Margin="0,0,0,10" Padding="5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="نوع التقرير:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" x:Name="ReportTypeComboBox" Margin="0,0,0,10" Padding="5">
                            <ComboBoxItem Content="IPTT" Tag="IPTT"/>
                            <ComboBoxItem Content="مالي" Tag="Financial"/>
                            <ComboBoxItem Content="أنشطة" Tag="Activities"/>
                            <ComboBoxItem Content="مشاريع" Tag="Projects"/>
                            <ComboBoxItem Content="مخصص" Tag="Custom"/>
                        </ComboBox>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="الوصف:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                        <TextBox Grid.Row="2" Grid.Column="1" x:Name="DescriptionTextBox" 
                                 Height="60" TextWrapping="Wrap" 
                                 AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                                 Margin="0,0,0,10" Padding="5"/>
                    </Grid>
                </GroupBox>

                <!-- معايير التقرير -->
                <GroupBox Header="معايير التقرير" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="النطاق الزمني:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="من:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <DatePicker x:Name="StartDatePicker" Width="120" Margin="0,0,10,0"/>
                            <TextBlock Text="إلى:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <DatePicker x:Name="EndDatePicker" Width="120"/>
                        </StackPanel>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="المشاريع:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,0,10">
                            <CheckBox x:Name="AllProjectsCheckBox" Content="جميع المشاريع" IsChecked="True" Margin="0,0,0,5"/>
                            <ListBox x:Name="ProjectsListBox" Height="80" IsEnabled="False">
                                <!-- سيتم ملؤها من الكود -->
                            </ListBox>
                        </StackPanel>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="المواقع:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="0,0,0,10">
                            <CheckBox x:Name="AllLocationsCheckBox" Content="جميع المواقع" IsChecked="True" Margin="0,0,0,5"/>
                            <StackPanel x:Name="LocationsPanel" Orientation="Horizontal" IsEnabled="False">
                                <CheckBox Content="الموقع 1" Margin="0,0,10,0"/>
                                <CheckBox Content="الموقع 2" Margin="0,0,10,0"/>
                                <CheckBox Content="الموقع 3" Margin="0,0,10,0"/>
                                <CheckBox Content="الموقع 4" Margin="0,0,10,0"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- خيارات إضافية -->
                <GroupBox Header="خيارات إضافية" Margin="0,0,0,15">
                    <StackPanel Margin="10">
                        <CheckBox x:Name="IncludeSummaryCheckBox" Content="تضمين ملخص" IsChecked="True" Margin="0,0,0,5"/>
                        <CheckBox x:Name="IncludeChartsCheckBox" Content="تضمين رسوم بيانية" Margin="0,0,0,5"/>
                        <CheckBox x:Name="GroupByProjectCheckBox" Content="تجميع حسب المشروع" IsChecked="True" Margin="0,0,0,5"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="GenerateButton" 
                        Content="🔄 إنشاء التقرير"
                        Click="GenerateButton_Click"
                        Background="#4CAF50"
                        Foreground="White"
                        Padding="20,10"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button x:Name="PreviewButton" 
                        Content="👁️ معاينة"
                        Click="PreviewButton_Click"
                        Background="#2196F3"
                        Foreground="White"
                        Padding="20,10"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
                
                <Button x:Name="CancelButton" 
                        Content="❌ إلغاء"
                        Click="CancelButton_Click"
                        Background="#F44336"
                        Foreground="White"
                        Padding="20,10"
                        FontSize="12"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Margin="5,0"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
