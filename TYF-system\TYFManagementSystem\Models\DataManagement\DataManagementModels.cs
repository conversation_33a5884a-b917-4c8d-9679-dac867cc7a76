using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TYFManagementSystem.Models.DataManagement
{
    /// <summary>
    /// ملخص المشروع مع الإحصائيات
    /// </summary>
    public class ProjectSummary : INotifyPropertyChanged
    {
        private string _status = "";
        private double _progressPercentage;

        public int Id { get; set; }
        public string ProjectNumber { get; set; } = "";
        public string Name { get; set; } = "";
        
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public decimal Budget { get; set; }
        public int Beneficiaries { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Manager { get; set; } = "";
        public string Region { get; set; } = "";
        public int IndicatorsCount { get; set; }
        public int CompletedIndicators { get; set; }
        
        public double ProgressPercentage
        {
            get => _progressPercentage;
            set => SetProperty(ref _progressPercentage, value);
        }

        // خصائص محسوبة
        public string BudgetFormatted => $"{Budget:N0} ريال";
        public string BeneficiariesFormatted => $"{Beneficiaries:N0} مستفيد";
        public string DurationFormatted => $"{(EndDate - StartDate).Days} يوم";
        public string ProgressFormatted => $"{ProgressPercentage:F1}%";
        public string IndicatorsFormatted => $"{CompletedIndicators}/{IndicatorsCount}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// إحصائيات IPTT للمشروع
    /// </summary>
    public class IpttProjectStats
    {
        public int TotalIndicators { get; set; }
        public int CompletedIndicators { get; set; }
        public double OverallProgress { get; set; }
    }

    /// <summary>
    /// إحصائيات قاعدة البيانات العامة
    /// </summary>
    public class DatabaseStatistics
    {
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int TotalBeneficiaries { get; set; }
        public decimal TotalBudget { get; set; }
        public int TotalReports { get; set; }
        public int TotalIpttProjects { get; set; }
        public int TotalIndicators { get; set; }

        public List<RegionStatistic> ProjectsByRegion { get; set; } = new List<RegionStatistic>();
        public List<StatusStatistic> ProjectsByStatus { get; set; } = new List<StatusStatistic>();

        // خصائص محسوبة
        public string TotalBudgetFormatted => $"{TotalBudget:N0} ريال";
        public string TotalBeneficiariesFormatted => $"{TotalBeneficiaries:N0} مستفيد";
        public double ActiveProjectsPercentage => TotalProjects > 0 ? Math.Round((double)ActiveProjects / TotalProjects * 100, 1) : 0;
        public double CompletedProjectsPercentage => TotalProjects > 0 ? Math.Round((double)CompletedProjects / TotalProjects * 100, 1) : 0;
    }

    /// <summary>
    /// إحصائيات المنطقة
    /// </summary>
    public class RegionStatistic
    {
        public string Region { get; set; } = "";
        public int ProjectCount { get; set; }
        public int TotalBeneficiaries { get; set; }
        public decimal TotalBudget { get; set; }

        public string TotalBudgetFormatted => $"{TotalBudget:N0} ريال";
        public string TotalBeneficiariesFormatted => $"{TotalBeneficiaries:N0} مستفيد";
    }

    /// <summary>
    /// إحصائيات الحالة
    /// </summary>
    public class StatusStatistic
    {
        public string Status { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }

        public string PercentageFormatted => $"{Percentage:F1}%";
    }

    /// <summary>
    /// معايير البحث والفلترة
    /// </summary>
    public class SearchCriteria : INotifyPropertyChanged
    {
        private string _searchTerm = "";
        private string _statusFilter = "الكل";
        private string _regionFilter = "الكل";
        private DateTime? _startDateFrom;
        private DateTime? _startDateTo;
        private DateTime? _endDateFrom;
        private DateTime? _endDateTo;
        private decimal? _budgetFrom;
        private decimal? _budgetTo;

        public string SearchTerm
        {
            get => _searchTerm;
            set => SetProperty(ref _searchTerm, value);
        }

        public string StatusFilter
        {
            get => _statusFilter;
            set => SetProperty(ref _statusFilter, value);
        }

        public string RegionFilter
        {
            get => _regionFilter;
            set => SetProperty(ref _regionFilter, value);
        }

        public DateTime? StartDateFrom
        {
            get => _startDateFrom;
            set => SetProperty(ref _startDateFrom, value);
        }

        public DateTime? StartDateTo
        {
            get => _startDateTo;
            set => SetProperty(ref _startDateTo, value);
        }

        public DateTime? EndDateFrom
        {
            get => _endDateFrom;
            set => SetProperty(ref _endDateFrom, value);
        }

        public DateTime? EndDateTo
        {
            get => _endDateTo;
            set => SetProperty(ref _endDateTo, value);
        }

        public decimal? BudgetFrom
        {
            get => _budgetFrom;
            set => SetProperty(ref _budgetFrom, value);
        }

        public decimal? BudgetTo
        {
            get => _budgetTo;
            set => SetProperty(ref _budgetTo, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// إعادة تعيين معايير البحث
        /// </summary>
        public void Reset()
        {
            SearchTerm = "";
            StatusFilter = "الكل";
            RegionFilter = "الكل";
            StartDateFrom = null;
            StartDateTo = null;
            EndDateFrom = null;
            EndDateTo = null;
            BudgetFrom = null;
            BudgetTo = null;
        }

        /// <summary>
        /// التحقق من وجود معايير بحث نشطة
        /// </summary>
        public bool HasActiveFilters => 
            !string.IsNullOrWhiteSpace(SearchTerm) ||
            StatusFilter != "الكل" ||
            RegionFilter != "الكل" ||
            StartDateFrom.HasValue ||
            StartDateTo.HasValue ||
            EndDateFrom.HasValue ||
            EndDateTo.HasValue ||
            BudgetFrom.HasValue ||
            BudgetTo.HasValue;
    }

    /// <summary>
    /// نتائج البحث
    /// </summary>
    public class SearchResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int FilteredCount { get; set; }
        public string SearchTerm { get; set; } = "";
        public TimeSpan SearchDuration { get; set; }

        public bool HasResults => Items.Count > 0;
        public string ResultsSummary => $"تم العثور على {FilteredCount} من أصل {TotalCount} عنصر";
    }

    /// <summary>
    /// معلومات الجدول
    /// </summary>
    public class TableInfo
    {
        public string TableName { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public int RecordCount { get; set; }
        public long SizeInBytes { get; set; }
        public DateTime LastModified { get; set; }

        public string SizeFormatted
        {
            get
            {
                if (SizeInBytes < 1024)
                    return $"{SizeInBytes} بايت";
                else if (SizeInBytes < 1024 * 1024)
                    return $"{SizeInBytes / 1024.0:F1} كيلوبايت";
                else
                    return $"{SizeInBytes / (1024.0 * 1024.0):F1} ميجابايت";
            }
        }

        public string LastModifiedFormatted => LastModified.ToString("yyyy-MM-dd HH:mm:ss");
    }
}
