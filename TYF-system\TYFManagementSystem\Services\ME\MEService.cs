using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models.ME;

namespace TYFManagementSystem.Services.ME
{
    /// <summary>
    /// خدمة نظام المتابعة والتقييم
    /// </summary>
    public class MEService
    {
        private readonly TyfDbContext _context;

        public MEService()
        {
            _context = new TyfDbContext();
        }

        /// <summary>
        /// الحصول على بيانات لوحة التحكم
        /// </summary>
        public async Task<MEDashboardData> GetDashboardDataAsync()
        {
            try
            {
                var dashboardData = new MEDashboardData();

                // تحميل ملخصات المشاريع
                dashboardData.ProjectSummaries = await GetProjectSummariesAsync();

                // تحميل تقدم المؤشرات
                dashboardData.IndicatorProgress = await GetIndicatorProgressAsync();

                // تحميل التنبيهات
                dashboardData.Alerts = await GenerateAlertsAsync();

                // حساب الإحصائيات العامة
                dashboardData.OverallStats = CalculateOverallStatistics(dashboardData.ProjectSummaries, dashboardData.IndicatorProgress);

                dashboardData.LastUpdated = DateTime.Now;

                return dashboardData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}");
                return new MEDashboardData();
            }
        }

        /// <summary>
        /// الحصول على ملخصات المشاريع
        /// </summary>
        private async Task<List<ProjectSummary>> GetProjectSummariesAsync()
        {
            try
            {
                var projects = await _context.IpttProjects
                    .Include(p => p.Indicators)
                    .ThenInclude(i => i.DataTypes)
                    .ToListAsync();

                var summaries = new List<ProjectSummary>();

                foreach (var project in projects)
                {
                    var summary = new ProjectSummary
                    {
                        ProjectId = project.ProjectId,
                        ProjectName = project.ProjectName,
                        TotalIndicators = project.Indicators.Count,
                        Status = ProjectStatus.Active, // افتراضي
                        StartDate = DateTime.Now.AddMonths(-6), // افتراضي
                        ProjectManager = "مدير المشروع" // افتراضي
                    };

                    // حساب نسبة الإنجاز
                    if (project.Indicators.Any())
                    {
                        var totalAchievement = project.Indicators.Sum(i =>
                        {
                            if (double.TryParse(i.AchievementPercentage, out double percentage))
                                return percentage;
                            return 0.0;
                        });
                        summary.CompletionPercentage = totalAchievement / project.Indicators.Count;
                    }

                    // حساب المؤشرات الرئيسية
                    summary.KeyIndicators = project.Indicators.Take(3).Select(i => new KeyIndicator
                    {
                        Name = i.IndicatorName,
                        Value = double.TryParse(i.Achievement, out double achievement) ? achievement : 0.0,
                        Target = double.TryParse(i.TotalTarget, out double target) ? target : 0.0,
                        Unit = "وحدة",
                        Status = (double.TryParse(i.AchievementPercentage, out double percentage) && percentage >= 80) ? IndicatorStatus.OnTrack :
                                (percentage >= 50) ? IndicatorStatus.BehindSchedule : IndicatorStatus.AtRisk
                    }).ToList();

                    // حساب الأنشطة (افتراضي)
                    summary.TotalActivities = project.Indicators.Sum(i => i.DataTypes.Count);
                    summary.CompletedActivities = (int)(summary.TotalActivities * summary.CompletionPercentage / 100);

                    // المواقع النشطة (افتراضي)
                    summary.ActiveLocations = 4; // افتراضي

                    // استخدام الميزانية (افتراضي)
                    summary.BudgetUtilization = summary.CompletionPercentage * 0.8; // افتراضي

                    summaries.Add(summary);
                }

                return summaries;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل ملخصات المشاريع: {ex.Message}");
                return new List<ProjectSummary>();
            }
        }

        /// <summary>
        /// الحصول على تقدم المؤشرات
        /// </summary>
        private async Task<List<IndicatorProgress>> GetIndicatorProgressAsync()
        {
            try
            {
                var indicators = await _context.IpttIndicators
                    .Include(i => i.Project)
                    .Include(i => i.DataTypes)
                    .ToListAsync();

                var progressList = new List<IndicatorProgress>();

                foreach (var indicator in indicators)
                {
                    var progress = new IndicatorProgress
                    {
                        IndicatorId = indicator.Id.ToString(),
                        IndicatorName = indicator.IndicatorName,
                        IndicatorNumber = indicator.IndicatorNumber,
                        ProjectId = indicator.ProjectId.ToString(),
                        ProjectName = indicator.Project?.ProjectName ?? "غير محدد",
                        Target = double.TryParse(indicator.TotalTarget, out double target) ? target : 0.0,
                        Achievement = double.TryParse(indicator.Achievement, out double achievement) ? achievement : 0.0,
                        PercentageAchieved = double.TryParse(indicator.AchievementPercentage, out double percentage) ? percentage : 0.0,
                        LastUpdated = DateTime.Now,
                        Unit = "وحدة",
                        Type = IndicatorType.Output
                    };

                    // تحديد حالة المؤشر
                    progress.Status = progress.PercentageAchieved >= 80 ? IndicatorStatus.OnTrack :
                                     progress.PercentageAchieved >= 50 ? IndicatorStatus.BehindSchedule : IndicatorStatus.AtRisk;

                    // إنشاء بيانات شهرية تجريبية
                    progress.MonthlyData = GenerateMonthlyData(progress.Achievement, progress.Target);

                    progressList.Add(progress);
                }

                return progressList;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل تقدم المؤشرات: {ex.Message}");
                return new List<IndicatorProgress>();
            }
        }

        /// <summary>
        /// إنشاء بيانات شهرية تجريبية
        /// </summary>
        private List<MonthlyProgress> GenerateMonthlyData(double totalAchievement, double totalTarget)
        {
            var monthlyData = new List<MonthlyProgress>();
            var currentDate = DateTime.Now;
            var monthlyTarget = totalTarget / 12; // توزيع الهدف على 12 شهر
            var monthlyAchievement = totalAchievement / 12; // توزيع الإنجاز على 12 شهر

            for (int i = 11; i >= 0; i--)
            {
                var date = currentDate.AddMonths(-i);
                var cumulativeTarget = monthlyTarget * (12 - i);
                var cumulativeAchievement = monthlyAchievement * (12 - i);

                monthlyData.Add(new MonthlyProgress
                {
                    Year = date.Year,
                    Month = date.Month,
                    MonthName = GetMonthName(date.Month),
                    Value = monthlyAchievement,
                    Target = monthlyTarget,
                    CumulativeValue = cumulativeAchievement,
                    CumulativeTarget = cumulativeTarget
                });
            }

            return monthlyData;
        }

        /// <summary>
        /// الحصول على اسم الشهر بالعربية
        /// </summary>
        private string GetMonthName(int month)
        {
            return month switch
            {
                1 => "يناير",
                2 => "فبراير",
                3 => "مارس",
                4 => "أبريل",
                5 => "مايو",
                6 => "يونيو",
                7 => "يوليو",
                8 => "أغسطس",
                9 => "سبتمبر",
                10 => "أكتوبر",
                11 => "نوفمبر",
                12 => "ديسمبر",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// إنشاء التنبيهات
        /// </summary>
        private async Task<List<MEAlert>> GenerateAlertsAsync()
        {
            var alerts = new List<MEAlert>();

            try
            {
                // تنبيهات المؤشرات المتأخرة
                var allIndicators = await _context.IpttIndicators
                    .Include(i => i.Project)
                    .ToListAsync();

                var behindScheduleIndicators = allIndicators
                    .Where(i => double.TryParse(i.AchievementPercentage, out double percentage) && percentage < 50)
                    .ToList();

                foreach (var indicator in behindScheduleIndicators)
                {
                    var percentage = double.TryParse(indicator.AchievementPercentage, out double p) ? p : 0.0;
                    alerts.Add(new MEAlert
                    {
                        Type = AlertType.IndicatorBehindSchedule,
                        Priority = percentage < 25 ? AlertPriority.Critical : AlertPriority.High,
                        Title = "مؤشر متأخر عن الجدول",
                        Description = $"المؤشر '{indicator.IndicatorName}' متأخر بنسبة {100 - percentage:F1}%",
                        ProjectId = indicator.ProjectId.ToString(),
                        ProjectName = indicator.Project?.ProjectName ?? "غير محدد",
                        ActionRequired = "مراجعة خطة العمل وتحديث الأنشطة"
                    });
                }

                // تنبيهات البيانات المفقودة
                var indicatorsWithoutData = await _context.IpttIndicators
                    .Include(i => i.Project)
                    .Where(i => string.IsNullOrEmpty(i.Achievement) || i.Achievement == "0")
                    .ToListAsync();

                foreach (var indicator in indicatorsWithoutData.Take(5)) // أول 5 فقط
                {
                    alerts.Add(new MEAlert
                    {
                        Type = AlertType.MissingData,
                        Priority = AlertPriority.Medium,
                        Title = "بيانات مفقودة",
                        Description = $"لا توجد بيانات للمؤشر '{indicator.IndicatorName}'",
                        ProjectId = indicator.ProjectId.ToString(),
                        ProjectName = indicator.Project?.ProjectName ?? "غير محدد",
                        ActionRequired = "إدخال البيانات المطلوبة"
                    });
                }

                // تنبيه عام للتقارير
                alerts.Add(new MEAlert
                {
                    Type = AlertType.ReportOverdue,
                    Priority = AlertPriority.Medium,
                    Title = "تقرير شهري مطلوب",
                    Description = "حان موعد إعداد التقرير الشهري لشهر " + GetMonthName(DateTime.Now.Month),
                    ActionRequired = "إعداد وإرسال التقرير الشهري"
                });

                return alerts.OrderByDescending(a => a.Priority).ThenByDescending(a => a.CreatedDate).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء التنبيهات: {ex.Message}");
                return alerts;
            }
        }

        /// <summary>
        /// حساب الإحصائيات العامة
        /// </summary>
        private OverallStatistics CalculateOverallStatistics(List<ProjectSummary> projects, List<IndicatorProgress> indicators)
        {
            var stats = new OverallStatistics();

            if (projects.Any())
            {
                stats.TotalProjects = projects.Count;
                stats.ActiveProjects = projects.Count(p => p.Status == ProjectStatus.Active);
                stats.CompletedProjects = projects.Count(p => p.Status == ProjectStatus.Completed);
                stats.OverallProgress = projects.Average(p => p.CompletionPercentage);
                stats.TotalActivities = projects.Sum(p => p.TotalActivities);
                stats.CompletedActivities = projects.Sum(p => p.CompletedActivities);
            }

            if (indicators.Any())
            {
                stats.TotalIndicators = indicators.Count;
                stats.OnTrackIndicators = indicators.Count(i => i.Status == IndicatorStatus.OnTrack);
                stats.BehindScheduleIndicators = indicators.Count(i => i.Status == IndicatorStatus.BehindSchedule || i.Status == IndicatorStatus.AtRisk);
            }

            // بيانات افتراضية
            stats.TotalBeneficiaries = 5000;
            stats.TotalBudget = 1000000;
            stats.UtilizedBudget = stats.TotalBudget * (stats.OverallProgress / 100) * 0.8;

            return stats;
        }

        /// <summary>
        /// الحصول على بيانات المؤشر حسب الفترة
        /// </summary>
        public async Task<List<MonthlyProgress>> GetIndicatorDataByPeriodAsync(string indicatorId, ReportingPeriod period, int year)
        {
            try
            {
                var indicator = await _context.IpttIndicators.FindAsync(indicatorId);
                if (indicator == null) return new List<MonthlyProgress>();

                // إنشاء بيانات تجريبية حسب الفترة
                var achievement = double.TryParse(indicator.Achievement, out double a) ? a : 0.0;
                var target = double.TryParse(indicator.TotalTarget, out double t) ? t : 0.0;
                return GenerateMonthlyData(achievement, target);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات المؤشر: {ex.Message}");
                return new List<MonthlyProgress>();
            }
        }

        /// <summary>
        /// تحديث حالة التنبيه
        /// </summary>
        public async Task<bool> MarkAlertAsReadAsync(string alertId)
        {
            try
            {
                // في التطبيق الحقيقي، سيتم حفظ التنبيهات في قاعدة البيانات
                await Task.Delay(100); // محاكاة عملية قاعدة البيانات
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التنبيه: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
