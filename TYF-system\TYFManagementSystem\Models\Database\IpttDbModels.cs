using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TYFManagementSystem.Models.Database
{
    /// <summary>
    /// نموذج قاعدة البيانات للمشاريع الأساسية
    /// </summary>
    [Table("Projects")]
    public class ProjectDb
    {
        [Key]
        public int Id { get; set; }

        [MaxLength(50)]
        public string ProjectNumber { get; set; } = "";

        [MaxLength(50)]
        public string ProjectCode { get; set; } = "";

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = "";

        [MaxLength(100)]
        public string Region { get; set; } = "";

        [MaxLength(1000)]
        public string Description { get; set; } = "";

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        [MaxLength(50)]
        public string Status { get; set; } = "";

        public decimal Budget { get; set; }

        [MaxLength(100)]
        public string Manager { get; set; } = "";

        public int Beneficiaries { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// نموذج قاعدة البيانات لحفظ بيانات IPTT للمشاريع
    /// </summary>
    [Table("IpttProjects")]
    public class IpttProjectDb
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string ProjectId { get; set; } = "";

        [Required]
        [MaxLength(200)]
        public string ProjectName { get; set; } = "";

        [MaxLength(100)]
        public string ProjectManager { get; set; } = "";

        [MaxLength(50)]
        public string Status { get; set; } = "";

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Budget { get; set; }

        [MaxLength(1000)]
        public string Description { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<IpttIndicatorDb> Indicators { get; set; } = new List<IpttIndicatorDb>();
        public virtual ICollection<IpttMonthColumnDb> MonthColumns { get; set; } = new List<IpttMonthColumnDb>();
    }

    /// <summary>
    /// نموذج قاعدة البيانات للمؤشرات
    /// </summary>
    [Table("IpttIndicators")]
    public class IpttIndicatorDb
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        [MaxLength(20)]
        public string IndicatorNumber { get; set; } = "";

        [Required]
        [MaxLength(500)]
        public string IndicatorName { get; set; } = "";

        [MaxLength(100)]
        public string TotalTarget { get; set; } = "";

        [MaxLength(100)]
        public string Achievement { get; set; } = "";

        [MaxLength(20)]
        public string AchievementPercentage { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual IpttProjectDb Project { get; set; } = null!;
        public virtual ICollection<IpttDataTypeDb> DataTypes { get; set; } = new List<IpttDataTypeDb>();
        public virtual ICollection<IpttMonthlyDataDb> MonthlyData { get; set; } = new List<IpttMonthlyDataDb>();
    }

    /// <summary>
    /// نموذج قاعدة البيانات لأنواع البيانات
    /// </summary>
    [Table("IpttDataTypes")]
    public class IpttDataTypeDb
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int IndicatorId { get; set; }

        [Required]
        [MaxLength(200)]
        public string DataTypeName { get; set; } = "";

        [MaxLength(100)]
        public string Target { get; set; } = "";

        [MaxLength(100)]
        public string Achievement { get; set; } = "";

        [MaxLength(20)]
        public string AchievementPercentage { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("IndicatorId")]
        public virtual IpttIndicatorDb Indicator { get; set; } = null!;
        public virtual ICollection<IpttMonthlyDataDb> MonthlyData { get; set; } = new List<IpttMonthlyDataDb>();
    }

    /// <summary>
    /// نموذج قاعدة البيانات للبيانات الشهرية
    /// </summary>
    [Table("IpttMonthlyData")]
    public class IpttMonthlyDataDb
    {
        [Key]
        public int Id { get; set; }

        public int? IndicatorId { get; set; }
        public int? DataTypeId { get; set; }

        [Required]
        [MaxLength(20)]
        public string MonthColumn { get; set; } = "";

        [MaxLength(100)]
        public string Value { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("IndicatorId")]
        public virtual IpttIndicatorDb? Indicator { get; set; }

        [ForeignKey("DataTypeId")]
        public virtual IpttDataTypeDb? DataType { get; set; }
    }

    /// <summary>
    /// نموذج قاعدة البيانات للأعمدة الشهرية
    /// </summary>
    [Table("IpttMonthColumns")]
    public class IpttMonthColumnDb
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        [MaxLength(20)]
        public string MonthColumn { get; set; } = "";

        public int DisplayOrder { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual IpttProjectDb Project { get; set; } = null!;
    }

    /// <summary>
    /// نموذج قاعدة البيانات لبيانات المواقع المتعددة
    /// </summary>
    [Table("IpttLocationData")]
    public class IpttLocationDataDb
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int LocationNumber { get; set; }

        [MaxLength(200)]
        public string LocationName { get; set; } = "";

        [Required]
        public int IndicatorId { get; set; }

        public int? DataTypeId { get; set; }

        [Required]
        [MaxLength(20)]
        public string MonthColumn { get; set; } = "";

        [MaxLength(100)]
        public string Value { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("ProjectId")]
        public virtual IpttProjectDb Project { get; set; } = null!;

        [ForeignKey("IndicatorId")]
        public virtual IpttIndicatorDb Indicator { get; set; } = null!;

        [ForeignKey("DataTypeId")]
        public virtual IpttDataTypeDb? DataType { get; set; }
    }
}
