using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows.Input;
using TYFManagementSystem.Services;

namespace TYFManagementSystem.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly LocalizationService _localizationService;

        [ObservableProperty]
        private string _currentPageTitle = "Dashboard";

        [ObservableProperty]
        private string _currentUser = "Admin User";

        [ObservableProperty]
        private bool _isMenuOpen = false;

        public ObservableCollection<MenuItemViewModel> MenuItems { get; }

        public ICommand ToggleMenuCommand { get; }
        public ICommand NavigateCommand { get; }
        public ICommand ChangeLanguageCommand { get; }
        public ICommand LogoutCommand { get; }

        public MainViewModel()
        {
            _localizationService = LocalizationService.Instance;
            _localizationService.LanguageChanged += OnLanguageChanged;

            MenuItems = new ObservableCollection<MenuItemViewModel>
            {
                new MenuItemViewModel { Title = GetLocalizedString("Dashboard"), Icon = "🏠", PageType = "Dashboard" },
                new MenuItemViewModel { Title = GetLocalizedString("Projects"), Icon = "📋", PageType = "Projects" },
                new MenuItemViewModel { Title = GetLocalizedString("Beneficiaries"), Icon = "👥", PageType = "Beneficiaries" },
                new MenuItemViewModel { Title = GetLocalizedString("Users"), Icon = "👤", PageType = "Users" },
                new MenuItemViewModel { Title = GetLocalizedString("Settings"), Icon = "⚙️", PageType = "Settings" }
            };

            ToggleMenuCommand = new RelayCommand(ToggleMenu);
            NavigateCommand = new RelayCommand<string>(Navigate);
            ChangeLanguageCommand = new RelayCommand<string>(ChangeLanguage);
            LogoutCommand = new RelayCommand(Logout);

            CurrentPageTitle = GetLocalizedString("Dashboard");
        }

        private void ToggleMenu()
        {
            IsMenuOpen = !IsMenuOpen;
        }

        private void Navigate(string? pageType)
        {
            if (string.IsNullOrEmpty(pageType)) return;

            CurrentPageTitle = GetLocalizedString(pageType);
            IsMenuOpen = false;

            // Here you would implement actual navigation logic
            // For now, we'll just update the title
        }

        private void ChangeLanguage(string? languageCode)
        {
            if (string.IsNullOrEmpty(languageCode)) return;

            _localizationService.SetLanguage(languageCode);
        }

        private void Logout()
        {
            // Implement logout logic
            System.Windows.Application.Current.Shutdown();
        }

        private void OnLanguageChanged(object? sender, EventArgs e)
        {
            // Update menu items when language changes
            foreach (var item in MenuItems)
            {
                item.Title = GetLocalizedString(item.PageType);
            }

            CurrentPageTitle = GetLocalizedString("Dashboard");
            OnPropertyChanged(nameof(MenuItems));
        }

        private string GetLocalizedString(string key)
        {
            return _localizationService.GetString(key);
        }
    }

    public partial class MenuItemViewModel : ObservableObject
    {
        [ObservableProperty]
        private string _title = string.Empty;

        [ObservableProperty]
        private string _icon = string.Empty;

        [ObservableProperty]
        private string _pageType = string.Empty;

        [ObservableProperty]
        private bool _isSelected = false;
    }
}
