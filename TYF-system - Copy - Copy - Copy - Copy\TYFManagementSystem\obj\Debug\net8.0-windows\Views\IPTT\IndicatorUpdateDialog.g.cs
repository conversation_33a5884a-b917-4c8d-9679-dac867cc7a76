﻿#pragma checksum "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BB9E22F3576F924551FFC6F9F7D7EA8BC5C8572C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace TYFManagementSystem.Views.IPTT {
    
    
    /// <summary>
    /// IndicatorUpdateDialog
    /// </summary>
    public partial class IndicatorUpdateDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 80 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IndicatorNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IndicatorNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitTextBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TargetValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrentValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DataSourceTextBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ResponsiblePersonTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TYFManagementSystem;component/views/iptt/indicatorupdatedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.IndicatorNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.IndicatorNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.UnitTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TargetValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CurrentValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.DataSourceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.ResponsiblePersonTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            
            #line 121 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Save_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 125 "..\..\..\..\..\Views\IPTT\IndicatorUpdateDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

