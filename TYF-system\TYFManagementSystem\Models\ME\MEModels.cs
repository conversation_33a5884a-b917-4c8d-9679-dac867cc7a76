using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TYFManagementSystem.Models.ME
{
    /// <summary>
    /// نموذج لوحة تحكم المتابعة والتقييم
    /// </summary>
    public class MEDashboardData
    {
        public List<ProjectSummary> ProjectSummaries { get; set; } = new();
        public List<IndicatorProgress> IndicatorProgress { get; set; } = new();
        public List<MEAlert> Alerts { get; set; } = new();
        public OverallStatistics OverallStats { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// ملخص المشروع
    /// </summary>
    public class ProjectSummary
    {
        public string ProjectId { get; set; } = "";
        public string ProjectName { get; set; } = "";
        public double CompletionPercentage { get; set; }
        public int TotalIndicators { get; set; }
        public int CompletedActivities { get; set; }
        public int TotalActivities { get; set; }
        public int ActiveLocations { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public ProjectStatus Status { get; set; }
        public List<KeyIndicator> KeyIndicators { get; set; } = new();
        public double BudgetUtilization { get; set; }
        public string ProjectManager { get; set; } = "";
    }

    /// <summary>
    /// تقدم المؤشر
    /// </summary>
    public class IndicatorProgress
    {
        public string IndicatorId { get; set; } = "";
        public string IndicatorName { get; set; } = "";
        public string IndicatorNumber { get; set; } = "";
        public string ProjectId { get; set; } = "";
        public string ProjectName { get; set; } = "";
        public double Target { get; set; }
        public double Achievement { get; set; }
        public double PercentageAchieved { get; set; }
        public DateTime LastUpdated { get; set; }
        public IndicatorStatus Status { get; set; }
        public List<MonthlyProgress> MonthlyData { get; set; } = new();
        public string Unit { get; set; } = "";
        public IndicatorType Type { get; set; }
    }

    /// <summary>
    /// التقدم الشهري
    /// </summary>
    public class MonthlyProgress
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public double Value { get; set; }
        public double Target { get; set; }
        public double CumulativeValue { get; set; }
        public double CumulativeTarget { get; set; }
        public string MonthName { get; set; } = "";
    }

    /// <summary>
    /// المؤشر الرئيسي
    /// </summary>
    public class KeyIndicator
    {
        public string Name { get; set; } = "";
        public double Value { get; set; }
        public double Target { get; set; }
        public string Unit { get; set; } = "";
        public IndicatorStatus Status { get; set; }
    }

    /// <summary>
    /// تنبيهات النظام
    /// </summary>
    public class MEAlert
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public AlertType Type { get; set; }
        public AlertPriority Priority { get; set; }
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string ProjectId { get; set; } = "";
        public string ProjectName { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsRead { get; set; } = false;
        public string ActionRequired { get; set; } = "";
    }

    /// <summary>
    /// الإحصائيات العامة
    /// </summary>
    public class OverallStatistics
    {
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int TotalIndicators { get; set; }
        public int OnTrackIndicators { get; set; }
        public int BehindScheduleIndicators { get; set; }
        public double OverallProgress { get; set; }
        public int TotalActivities { get; set; }
        public int CompletedActivities { get; set; }
        public int TotalBeneficiaries { get; set; }
        public double TotalBudget { get; set; }
        public double UtilizedBudget { get; set; }
    }

    /// <summary>
    /// ربط النشاط بالمؤشر
    /// </summary>
    public class ActivityIndicatorLink
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ActivityId { get; set; } = "";
        public string ActivityName { get; set; } = "";
        public string IndicatorId { get; set; } = "";
        public string IndicatorName { get; set; } = "";
        public string ProjectId { get; set; } = "";
        public double ContributionWeight { get; set; } = 1.0; // وزن مساهمة النشاط في المؤشر
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// بيانات الأداء الزمنية
    /// </summary>
    public class PerformanceData
    {
        public string ProjectId { get; set; } = "";
        public string IndicatorId { get; set; } = "";
        public DateTime Date { get; set; }
        public double Value { get; set; }
        public double Target { get; set; }
        public string DataSource { get; set; } = "";
        public string Notes { get; set; } = "";
        public bool IsVerified { get; set; } = false;
    }

    /// <summary>
    /// حالة المشروع
    /// </summary>
    public enum ProjectStatus
    {
        Planning = 1,       // تخطيط
        Active = 2,         // نشط
        OnHold = 3,         // متوقف مؤقتاً
        Completed = 4,      // مكتمل
        Cancelled = 5       // ملغي
    }

    /// <summary>
    /// حالة المؤشر
    /// </summary>
    public enum IndicatorStatus
    {
        OnTrack = 1,        // على المسار الصحيح
        BehindSchedule = 2, // متأخر عن الجدول
        AtRisk = 3,         // في خطر
        Achieved = 4,       // تم تحقيقه
        NotStarted = 5      // لم يبدأ
    }

    /// <summary>
    /// نوع المؤشر
    /// </summary>
    public enum IndicatorType
    {
        Output = 1,         // مخرجات
        Outcome = 2,        // نتائج
        Impact = 3,         // تأثير
        Process = 4         // عملية
    }

    /// <summary>
    /// نوع التنبيه
    /// </summary>
    public enum AlertType
    {
        IndicatorBehindSchedule = 1,    // مؤشر متأخر
        MissingData = 2,                // بيانات مفقودة
        ReportOverdue = 3,              // تقرير متأخر
        BudgetExceeded = 4,             // تجاوز الميزانية
        ActivityDelayed = 5,            // نشاط متأخر
        DataQualityIssue = 6           // مشكلة في جودة البيانات
    }

    /// <summary>
    /// أولوية التنبيه
    /// </summary>
    public enum AlertPriority
    {
        Low = 1,        // منخفضة
        Medium = 2,     // متوسطة
        High = 3,       // عالية
        Critical = 4    // حرجة
    }

    /// <summary>
    /// فترة التقرير
    /// </summary>
    public enum ReportingPeriod
    {
        Monthly = 1,    // شهري
        Quarterly = 2,  // ربع سنوي
        Yearly = 3      // سنوي
    }
}
