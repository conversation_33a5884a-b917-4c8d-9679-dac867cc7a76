<Window x:Class="TYFManagementSystem.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام إدارة مؤسسة تمدين شباب"
        Height="768" Width="1366"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="#F5F5F5"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/SidebarButtonStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280" MinWidth="250" MaxWidth="350"/>
            <ColumnDefinition Width="*" MinWidth="600"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0" Background="#2E7D32" Padding="15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0" Background="#4CAF50" Padding="20" Margin="0,0,0,25" CornerRadius="8">
                    <Border.Effect>
                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="مؤسسة تمدين شباب"
                                 Foreground="White"
                                 FontSize="18"
                                 FontWeight="Bold"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="نظام الإدارة المتكامل"
                                 Foreground="White"
                                 FontSize="13"
                                 HorizontalAlignment="Center"
                                 Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Menu Buttons -->
                <StackPanel Grid.Row="1">
                    <Button Content="لوحة التحكم"
                            Click="DashboardButton_Click"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="إدارة المشاريع"
                            Click="ProjectsButton_Click"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="إدارة التقارير"
                            Click="ReportsButton_Click"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="إدارة البيانات والمعلومات"
                            Click="DataManagementButton_Click"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="نظام متابعة وتقييم (M&amp;E)"
                            Click="MEButton_Click"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="إدارة الأنشطة الميدانية"
                            Click="ActivitiesButton_Click"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="النسخ الاحتياطي"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>

                    <Button Content="إدارة المستخدمين"
                            Style="{StaticResource EnhancedSidebarButtonStyle}"/>
                </StackPanel>

                <!-- Footer -->
                <Border Grid.Row="2" Background="#1B5E20" Padding="15,10" CornerRadius="6" Margin="0,20,0,0">
                    <StackPanel>
                        <TextBlock Text="الإصدار 1.0"
                                 Foreground="White"
                                 FontSize="11"
                                 HorizontalAlignment="Center"/>
                        <TextBlock Text="2024 ©"
                                 Foreground="White"
                                 FontSize="10"
                                 HorizontalAlignment="Center"
                                 Margin="0,2,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ContentControl x:Name="MainContentArea" Grid.Column="1" Margin="25">
            <!-- Default Dashboard Content -->
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <Border Background="White"
                        CornerRadius="12"
                        Padding="40"
                        Margin="0,0,0,20">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.25" BlurRadius="12"/>
                    </Border.Effect>

                    <StackPanel>
                        <TextBlock Text="مرحباً بك في نظام إدارة مؤسسة تمدين شباب"
                                 FontSize="28"
                                 FontWeight="Bold"
                                 Foreground="#2E7D32"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,15"
                                 TextWrapping="Wrap"/>

                        <TextBlock Text="نظام إدارة متكامل للمؤسسات الإنسانية"
                                 FontSize="18"
                                 Foreground="#757575"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,50"
                                 TextWrapping="Wrap"/>

                        <!-- Statistics Cards -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" MinWidth="200"/>
                                <ColumnDefinition Width="*" MinWidth="200"/>
                                <ColumnDefinition Width="*" MinWidth="200"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#2E7D32"
                                    CornerRadius="12" Padding="25" Margin="10">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Border.Effect>
                                <StackPanel>
                                    <TextBlock Text="المشاريع النشطة"
                                             Foreground="White"
                                             FontSize="16"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             TextWrapping="Wrap"/>
                                    <TextBlock Text="12"
                                             Foreground="White"
                                             FontSize="36"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             Margin="0,15,0,0"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="1" Background="#4CAF50"
                                    CornerRadius="12" Padding="25" Margin="10">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Border.Effect>
                                <StackPanel>
                                    <TextBlock Text="المستفيدين"
                                             Foreground="White"
                                             FontSize="16"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             TextWrapping="Wrap"/>
                                    <TextBlock Text="1,245"
                                             Foreground="White"
                                             FontSize="36"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             Margin="0,15,0,0"/>
                                </StackPanel>
                            </Border>

                            <Border Grid.Column="2" Background="#81C784"
                                    CornerRadius="12" Padding="25" Margin="10">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                                </Border.Effect>
                                <StackPanel>
                                    <TextBlock Text="التقارير الشهرية"
                                             Foreground="White"
                                             FontSize="16"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             TextWrapping="Wrap"/>
                                    <TextBlock Text="8"
                                             Foreground="White"
                                             FontSize="36"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             Margin="0,15,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
            </ScrollViewer>
        </ContentControl>
    </Grid>
</Window>
