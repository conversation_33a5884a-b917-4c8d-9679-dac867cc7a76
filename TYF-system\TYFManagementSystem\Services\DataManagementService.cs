using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.Database;
using TYFManagementSystem.Models.Reports;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة البيانات والمعلومات العامة
    /// </summary>
    public class DataManagementService
    {
        private readonly TyfDbContext _context;

        public DataManagementService()
        {
            _context = new TyfDbContext();
            InitializeDatabaseAsync().Wait();
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.EnsureDatabaseCreatedAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        #region Projects Management
        /// <summary>
        /// الحصول على جميع المشاريع مع إحصائيات
        /// </summary>
        public async Task<List<ProjectSummary>> GetProjectsSummaryAsync()
        {
            try
            {
                var projects = await _context.Projects.ToListAsync();
                var projectSummaries = new List<ProjectSummary>();

                foreach (var project in projects)
                {
                    // حساب إحصائيات IPTT للمشروع
                    var ipttStats = await GetProjectIpttStatsAsync(project.Id.ToString());
                    
                    var summary = new ProjectSummary
                    {
                        Id = project.Id,
                        ProjectNumber = project.ProjectNumber,
                        Name = project.Name,
                        Status = project.Status,
                        Budget = project.Budget,
                        Beneficiaries = project.Beneficiaries,
                        StartDate = project.StartDate,
                        EndDate = project.EndDate,
                        Manager = project.Manager,
                        Region = project.Region,
                        IndicatorsCount = ipttStats.TotalIndicators,
                        CompletedIndicators = ipttStats.CompletedIndicators,
                        ProgressPercentage = ipttStats.OverallProgress
                    };

                    projectSummaries.Add(summary);
                }

                return projectSummaries;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب ملخص المشاريع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على إحصائيات IPTT للمشروع
        /// </summary>
        private async Task<IpttProjectStats> GetProjectIpttStatsAsync(string projectId)
        {
            try
            {
                var ipttProject = await _context.IpttProjects
                    .Include(p => p.Indicators)
                    .FirstOrDefaultAsync(p => p.ProjectId == projectId);

                if (ipttProject == null)
                {
                    return new IpttProjectStats();
                }

                var totalIndicators = ipttProject.Indicators.Count;
                var completedIndicators = 0;
                var totalProgress = 0.0;

                // حساب التقدم لكل مؤشر
                foreach (var indicator in ipttProject.Indicators)
                {
                    var monthlyData = await _context.IpttMonthlyData
                        .Where(m => m.IndicatorId == indicator.Id)
                        .ToListAsync();

                    if (monthlyData.Any())
                    {
                        var hasData = monthlyData.Any(m => !string.IsNullOrEmpty(m.Value) && m.Value != "0");
                        if (hasData)
                        {
                            completedIndicators++;
                            // حساب نسبة التقدم بناءً على البيانات المتاحة
                            var dataCount = monthlyData.Count(m => !string.IsNullOrEmpty(m.Value) && m.Value != "0");
                            var progressPercent = (double)dataCount / monthlyData.Count * 100;
                            totalProgress += progressPercent;
                        }
                    }
                }

                var overallProgress = totalIndicators > 0 ? totalProgress / totalIndicators : 0;

                return new IpttProjectStats
                {
                    TotalIndicators = totalIndicators,
                    CompletedIndicators = completedIndicators,
                    OverallProgress = Math.Round(overallProgress, 1)
                };
            }
            catch
            {
                return new IpttProjectStats();
            }
        }
        #endregion

        #region Reports Management
        /// <summary>
        /// الحصول على جميع التقارير
        /// </summary>
        public async Task<List<Report>> GetAllReportsAsync()
        {
            try
            {
                return await _context.Reports
                    .OrderByDescending(r => r.CreatedDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب التقارير: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف تقرير
        /// </summary>
        public async Task<bool> DeleteReportAsync(int reportId)
        {
            try
            {
                var report = await _context.Reports.FindAsync(reportId);
                if (report != null)
                {
                    _context.Reports.Remove(report);
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حذف التقرير: {ex.Message}", ex);
            }
        }
        #endregion

        #region Database Statistics
        /// <summary>
        /// الحصول على إحصائيات قاعدة البيانات العامة
        /// </summary>
        public async Task<DatabaseStatistics> GetDatabaseStatisticsAsync()
        {
            try
            {
                var stats = new DatabaseStatistics
                {
                    TotalProjects = await _context.Projects.CountAsync(),
                    ActiveProjects = await _context.Projects.CountAsync(p => p.Status == "نشط" || p.Status == "Active"),
                    CompletedProjects = await _context.Projects.CountAsync(p => p.Status == "مكتمل" || p.Status == "Completed"),
                    TotalBeneficiaries = await _context.Projects.SumAsync(p => p.Beneficiaries),
                    TotalBudget = await _context.Projects.SumAsync(p => p.Budget),
                    TotalReports = await _context.Reports.CountAsync(),
                    TotalIpttProjects = await _context.IpttProjects.CountAsync(),
                    TotalIndicators = await _context.IpttIndicators.CountAsync()
                };

                // حساب المشاريع حسب المنطقة
                stats.ProjectsByRegion = await _context.Projects
                    .GroupBy(p => p.Region)
                    .Select(g => new RegionStatistic
                    {
                        Region = g.Key,
                        ProjectCount = g.Count(),
                        TotalBeneficiaries = g.Sum(p => p.Beneficiaries),
                        TotalBudget = g.Sum(p => p.Budget)
                    })
                    .ToListAsync();

                // حساب المشاريع حسب الحالة
                stats.ProjectsByStatus = await _context.Projects
                    .GroupBy(p => p.Status)
                    .Select(g => new StatusStatistic
                    {
                        Status = g.Key,
                        Count = g.Count(),
                        Percentage = 0 // سيتم حسابها لاحقاً
                    })
                    .ToListAsync();

                // حساب النسب المئوية للحالات
                foreach (var statusStat in stats.ProjectsByStatus)
                {
                    statusStat.Percentage = stats.TotalProjects > 0 
                        ? Math.Round((double)statusStat.Count / stats.TotalProjects * 100, 1) 
                        : 0;
                }

                return stats;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في جلب إحصائيات قاعدة البيانات: {ex.Message}", ex);
            }
        }
        #endregion

        #region Search and Filter
        /// <summary>
        /// البحث في المشاريع
        /// </summary>
        public async Task<List<ProjectSummary>> SearchProjectsAsync(string searchTerm, string? statusFilter = null, string? regionFilter = null)
        {
            try
            {
                var query = _context.Projects.AsQueryable();

                // تطبيق فلتر البحث
                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    query = query.Where(p => 
                        p.Name.Contains(searchTerm) ||
                        p.ProjectNumber.Contains(searchTerm) ||
                        p.ProjectCode.Contains(searchTerm) ||
                        p.Manager.Contains(searchTerm) ||
                        p.Description.Contains(searchTerm));
                }

                // تطبيق فلتر الحالة
                if (!string.IsNullOrWhiteSpace(statusFilter) && statusFilter != "الكل")
                {
                    query = query.Where(p => p.Status == statusFilter);
                }

                // تطبيق فلتر المنطقة
                if (!string.IsNullOrWhiteSpace(regionFilter) && regionFilter != "الكل")
                {
                    query = query.Where(p => p.Region == regionFilter);
                }

                var projects = await query.ToListAsync();
                var projectSummaries = new List<ProjectSummary>();

                foreach (var project in projects)
                {
                    var ipttStats = await GetProjectIpttStatsAsync(project.Id.ToString());
                    
                    var summary = new ProjectSummary
                    {
                        Id = project.Id,
                        ProjectNumber = project.ProjectNumber,
                        Name = project.Name,
                        Status = project.Status,
                        Budget = project.Budget,
                        Beneficiaries = project.Beneficiaries,
                        StartDate = project.StartDate,
                        EndDate = project.EndDate,
                        Manager = project.Manager,
                        Region = project.Region,
                        IndicatorsCount = ipttStats.TotalIndicators,
                        CompletedIndicators = ipttStats.CompletedIndicators,
                        ProgressPercentage = ipttStats.OverallProgress
                    };

                    projectSummaries.Add(summary);
                }

                return projectSummaries;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في البحث في المشاريع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على قائمة المناطق المتاحة
        /// </summary>
        public async Task<List<string>> GetAvailableRegionsAsync()
        {
            try
            {
                return await _context.Projects
                    .Where(p => !string.IsNullOrEmpty(p.Region))
                    .Select(p => p.Region)
                    .Distinct()
                    .OrderBy(r => r)
                    .ToListAsync();
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// الحصول على قائمة الحالات المتاحة
        /// </summary>
        public async Task<List<string>> GetAvailableStatusesAsync()
        {
            try
            {
                return await _context.Projects
                    .Where(p => !string.IsNullOrEmpty(p.Status))
                    .Select(p => p.Status)
                    .Distinct()
                    .OrderBy(s => s)
                    .ToListAsync();
            }
            catch
            {
                return new List<string>();
            }
        }
        #endregion

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
