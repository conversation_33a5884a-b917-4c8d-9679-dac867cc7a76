using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Models;

namespace TYFManagementSystem.Views.Projects
{
    /// <summary>
    /// Interaction logic for ProjectDialog.xaml
    /// </summary>
    public partial class ProjectDialog : Window
    {
        private readonly Project _project;
        private readonly bool _isNewProject;

        public ProjectDialog(Project project, bool isNewProject)
        {
            InitializeComponent();
            _project = project;
            _isNewProject = isNewProject;
            
            InitializeDialog();
            LoadProjectData();
        }

        private void InitializeDialog()
        {
            HeaderText.Text = _isNewProject ? "إضافة مشروع جديد" : "تعديل المشروع";
            Title = _isNewProject ? "إضافة مشروع جديد" : "تعديل المشروع";
        }

        private void LoadProjectData()
        {
            // Set project number (read-only)
            if (_isNewProject)
            {
                ProjectNumberTextBox.Text = "سيتم توليده تلقائياً";
            }
            else
            {
                ProjectNumberTextBox.Text = _project.ProjectNumber;
            }

            ProjectCodeTextBox.Text = _project.ProjectCode;
            NameTextBox.Text = _project.Name;
            RegionTextBox.Text = _project.Region;
            DescriptionTextBox.Text = _project.Description;
            StartDatePicker.SelectedDate = _project.StartDate;
            EndDatePicker.SelectedDate = _project.EndDate;
            BudgetTextBox.Text = _project.Budget.ToString("F2");
            ManagerTextBox.Text = _project.Manager;
            BeneficiariesTextBox.Text = _project.Beneficiaries.ToString();

            // Set status
            foreach (ComboBoxItem item in StatusComboBox.Items)
            {
                if (item.Content.ToString() == _project.Status)
                {
                    StatusComboBox.SelectedItem = item;
                    break;
                }
            }

            // If no status is selected, select the first one
            if (StatusComboBox.SelectedItem == null && StatusComboBox.Items.Count > 0)
            {
                StatusComboBox.SelectedIndex = 0;
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                SaveProjectData();
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ProjectCodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال كود المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProjectCodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(RegionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال منطقة المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                RegionTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                DescriptionTextBox.Focus();
                return false;
            }

            if (!StartDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ بداية المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StartDatePicker.Focus();
                return false;
            }

            if (!EndDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ نهاية المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                EndDatePicker.Focus();
                return false;
            }

            if (EndDatePicker.SelectedDate < StartDatePicker.SelectedDate)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                EndDatePicker.Focus();
                return false;
            }

            if (!decimal.TryParse(BudgetTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للميزانية", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                BudgetTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ManagerTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم مدير المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ManagerTextBox.Focus();
                return false;
            }

            if (!int.TryParse(BeneficiariesTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال عدد صحيح للمستفيدين", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                BeneficiariesTextBox.Focus();
                return false;
            }

            if (StatusComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة المشروع", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusComboBox.Focus();
                return false;
            }

            return true;
        }

        private void SaveProjectData()
        {
            // Don't update ProjectNumber for existing projects (it's auto-generated)
            if (!_isNewProject)
            {
                _project.ProjectNumber = ProjectNumberTextBox.Text.Trim();
            }

            _project.ProjectCode = ProjectCodeTextBox.Text.Trim();
            _project.Name = NameTextBox.Text.Trim();
            _project.Region = RegionTextBox.Text.Trim();
            _project.Description = DescriptionTextBox.Text.Trim();
            _project.StartDate = StartDatePicker.SelectedDate!.Value;
            _project.EndDate = EndDatePicker.SelectedDate!.Value;
            _project.Budget = decimal.Parse(BudgetTextBox.Text);
            _project.Manager = ManagerTextBox.Text.Trim();
            _project.Beneficiaries = int.Parse(BeneficiariesTextBox.Text);
            _project.Status = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString()!;
        }
    }
}
