using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TYFManagementSystem.Models.Reports;
using System.Text.Json;
using OfficeOpenXml;
using Microsoft.Win32;
using System.Windows;

namespace TYFManagementSystem.Services.Reports
{
    /// <summary>
    /// خدمة تصدير التقارير بصيغ متعددة
    /// </summary>
    public class ReportExportService
    {
        public ReportExportService()
        {
            // تعيين سياق الترخيص لـ EPPlus
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// تصدير التقرير بالصيغة المحددة
        /// </summary>
        public async Task<bool> ExportReportAsync(ReportGenerationResult reportResult, ExportFormat format, string? filePath = null)
        {
            try
            {
                if (reportResult?.Report == null || reportResult.Data == null)
                    return false;

                // إذا لم يتم تحديد مسار، اطلب من المستخدم اختيار المسار
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = GetSaveFilePath(format, reportResult.Report.Name);
                    if (string.IsNullOrEmpty(filePath))
                        return false; // المستخدم ألغى العملية
                }

                switch (format)
                {
                    case ExportFormat.Excel:
                        return await ExportToExcelAsync(reportResult, filePath);
                    case ExportFormat.CSV:
                        return await ExportToCsvAsync(reportResult, filePath);
                    case ExportFormat.JSON:
                        return await ExportToJsonAsync(reportResult, filePath);
                    case ExportFormat.XML:
                        return await ExportToXmlAsync(reportResult, filePath);
                    case ExportFormat.PDF:
                        return await ExportToPdfAsync(reportResult, filePath);
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private async Task<bool> ExportToExcelAsync(ReportGenerationResult reportResult, string filePath)
        {
            try
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("التقرير");

                // إضافة معلومات التقرير
                worksheet.Cells[1, 1].Value = "اسم التقرير:";
                worksheet.Cells[1, 2].Value = reportResult.Report?.Name;
                worksheet.Cells[2, 1].Value = "تاريخ الإنشاء:";
                worksheet.Cells[2, 2].Value = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                worksheet.Cells[3, 1].Value = "عدد السجلات:";
                worksheet.Cells[3, 2].Value = reportResult.RecordCount;

                // إضافة البيانات
                if (reportResult.Data.Any())
                {
                    var startRow = 5;
                    var firstRow = reportResult.Data.First();
                    var columns = firstRow.Values.Keys.ToList();

                    // إضافة رؤوس الأعمدة
                    for (int i = 0; i < columns.Count; i++)
                    {
                        worksheet.Cells[startRow, i + 1].Value = columns[i];
                        worksheet.Cells[startRow, i + 1].Style.Font.Bold = true;
                        worksheet.Cells[startRow, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                        worksheet.Cells[startRow, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    }

                    // إضافة البيانات
                    for (int row = 0; row < reportResult.Data.Count; row++)
                    {
                        var dataRow = reportResult.Data[row];
                        for (int col = 0; col < columns.Count; col++)
                        {
                            var value = dataRow.Values.ContainsKey(columns[col]) ? dataRow.Values[columns[col]] : "";
                            worksheet.Cells[startRow + row + 1, col + 1].Value = value?.ToString();
                        }
                    }

                    // تنسيق الجدول
                    var range = worksheet.Cells[startRow, 1, startRow + reportResult.Data.Count, columns.Count];
                    range.Style.Border.Top.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    range.Style.Border.Left.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    range.Style.Border.Right.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                    range.Style.Border.Bottom.Style = OfficeOpenXml.Style.ExcelBorderStyle.Thin;
                }

                // ضبط عرض الأعمدة تلقائياً
                worksheet.Cells.AutoFitColumns();

                // حفظ الملف
                await package.SaveAsAsync(new FileInfo(filePath));
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير Excel: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير إلى CSV
        /// </summary>
        private async Task<bool> ExportToCsvAsync(ReportGenerationResult reportResult, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // إضافة معلومات التقرير
                csv.AppendLine($"اسم التقرير,{reportResult.Report?.Name}");
                csv.AppendLine($"تاريخ الإنشاء,{DateTime.Now:dd/MM/yyyy HH:mm}");
                csv.AppendLine($"عدد السجلات,{reportResult.RecordCount}");
                csv.AppendLine(); // سطر فارغ

                if (reportResult.Data.Any())
                {
                    var firstRow = reportResult.Data.First();
                    var columns = firstRow.Values.Keys.ToList();

                    // إضافة رؤوس الأعمدة
                    csv.AppendLine(string.Join(",", columns.Select(c => $"\"{c}\"")));

                    // إضافة البيانات
                    foreach (var dataRow in reportResult.Data)
                    {
                        var values = columns.Select(col => 
                        {
                            var value = dataRow.Values.ContainsKey(col) ? dataRow.Values[col]?.ToString() : "";
                            return $"\"{value?.Replace("\"", "\"\"")}\""; // تجنب مشاكل الفواصل والاقتباسات
                        });
                        csv.AppendLine(string.Join(",", values));
                    }
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير CSV: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير إلى JSON
        /// </summary>
        private async Task<bool> ExportToJsonAsync(ReportGenerationResult reportResult, string filePath)
        {
            try
            {
                var exportData = new
                {
                    ReportInfo = new
                    {
                        Name = reportResult.Report?.Name,
                        Type = reportResult.Report?.Type.ToString(),
                        CreatedDate = DateTime.Now,
                        RecordCount = reportResult.RecordCount,
                        GenerationTime = reportResult.GenerationTime.TotalSeconds
                    },
                    Data = reportResult.Data
                };

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(exportData, options);
                await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير JSON: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير إلى XML
        /// </summary>
        private async Task<bool> ExportToXmlAsync(ReportGenerationResult reportResult, string filePath)
        {
            try
            {
                var xml = new StringBuilder();
                xml.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xml.AppendLine("<Report>");
                
                // معلومات التقرير
                xml.AppendLine("  <ReportInfo>");
                xml.AppendLine($"    <Name>{System.Security.SecurityElement.Escape(reportResult.Report?.Name ?? "")}</Name>");
                xml.AppendLine($"    <Type>{reportResult.Report?.Type}</Type>");
                xml.AppendLine($"    <CreatedDate>{DateTime.Now:yyyy-MM-dd HH:mm:ss}</CreatedDate>");
                xml.AppendLine($"    <RecordCount>{reportResult.RecordCount}</RecordCount>");
                xml.AppendLine("  </ReportInfo>");

                // البيانات
                xml.AppendLine("  <Data>");
                foreach (var dataRow in reportResult.Data)
                {
                    xml.AppendLine("    <Row>");
                    foreach (var kvp in dataRow.Values)
                    {
                        var elementName = kvp.Key.Replace(" ", "_").Replace(".", "_");
                        var value = System.Security.SecurityElement.Escape(kvp.Value?.ToString() ?? "");
                        xml.AppendLine($"      <{elementName}>{value}</{elementName}>");
                    }
                    xml.AppendLine("    </Row>");
                }
                xml.AppendLine("  </Data>");
                xml.AppendLine("</Report>");

                await File.WriteAllTextAsync(filePath, xml.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير XML: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير إلى PDF (مبسط)
        /// </summary>
        private async Task<bool> ExportToPdfAsync(ReportGenerationResult reportResult, string filePath)
        {
            try
            {
                // للتبسيط، سنصدر كـ HTML ثم نطلب من المستخدم طباعته كـ PDF
                var html = GenerateHtmlReport(reportResult);
                var htmlPath = Path.ChangeExtension(filePath, ".html");
                
                await File.WriteAllTextAsync(htmlPath, html, Encoding.UTF8);
                
                MessageBox.Show($"تم إنشاء ملف HTML في:\n{htmlPath}\n\nيمكنك فتحه في المتصفح وطباعته كـ PDF", 
                    "تصدير PDF", MessageBoxButton.OK, MessageBoxImage.Information);
                
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير PDF: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء تقرير HTML
        /// </summary>
        private string GenerateHtmlReport(ReportGenerationResult reportResult)
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html dir='rtl' lang='ar'>");
            html.AppendLine("<head>");
            html.AppendLine("    <meta charset='UTF-8'>");
            html.AppendLine("    <title>تقرير نظام إدارة مؤسسة تمدين شباب</title>");
            html.AppendLine("    <style>");
            html.AppendLine("        body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine("        .header { background: #2E7D32; color: white; padding: 20px; text-align: center; }");
            html.AppendLine("        .info { margin: 20px 0; }");
            html.AppendLine("        table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
            html.AppendLine("        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
            html.AppendLine("        th { background-color: #f2f2f2; font-weight: bold; }");
            html.AppendLine("        .footer { margin-top: 30px; text-align: center; color: #666; }");
            html.AppendLine("    </style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            // رأس التقرير
            html.AppendLine("    <div class='header'>");
            html.AppendLine("        <h1>نظام إدارة مؤسسة تمدين شباب</h1>");
            html.AppendLine($"        <h2>{reportResult.Report?.Name}</h2>");
            html.AppendLine("    </div>");

            // معلومات التقرير
            html.AppendLine("    <div class='info'>");
            html.AppendLine($"        <p><strong>تاريخ الإنشاء:</strong> {DateTime.Now:dd/MM/yyyy HH:mm}</p>");
            html.AppendLine($"        <p><strong>عدد السجلات:</strong> {reportResult.RecordCount}</p>");
            html.AppendLine($"        <p><strong>وقت الإنشاء:</strong> {reportResult.GenerationTime.TotalSeconds:F1} ثانية</p>");
            html.AppendLine("    </div>");

            // جدول البيانات
            if (reportResult.Data.Any())
            {
                html.AppendLine("    <table>");
                
                // رؤوس الأعمدة
                var firstRow = reportResult.Data.First();
                html.AppendLine("        <thead><tr>");
                foreach (var column in firstRow.Values.Keys)
                {
                    html.AppendLine($"            <th>{System.Security.SecurityElement.Escape(column)}</th>");
                }
                html.AppendLine("        </tr></thead>");

                // البيانات
                html.AppendLine("        <tbody>");
                foreach (var dataRow in reportResult.Data)
                {
                    html.AppendLine("            <tr>");
                    foreach (var column in firstRow.Values.Keys)
                    {
                        var value = dataRow.Values.ContainsKey(column) ? dataRow.Values[column]?.ToString() : "";
                        html.AppendLine($"                <td>{System.Security.SecurityElement.Escape(value ?? "")}</td>");
                    }
                    html.AppendLine("            </tr>");
                }
                html.AppendLine("        </tbody>");
                html.AppendLine("    </table>");
            }

            // تذييل التقرير
            html.AppendLine("    <div class='footer'>");
            html.AppendLine("        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مؤسسة تمدين شباب</p>");
            html.AppendLine("    </div>");
            
            html.AppendLine("</body>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        /// <summary>
        /// الحصول على مسار حفظ الملف
        /// </summary>
        private string? GetSaveFilePath(ExportFormat format, string reportName)
        {
            var saveDialog = new SaveFileDialog
            {
                Title = "حفظ التقرير",
                FileName = $"{reportName}_{DateTime.Now:yyyyMMdd_HHmm}"
            };

            switch (format)
            {
                case ExportFormat.Excel:
                    saveDialog.Filter = "ملفات Excel (*.xlsx)|*.xlsx";
                    saveDialog.DefaultExt = ".xlsx";
                    break;
                case ExportFormat.CSV:
                    saveDialog.Filter = "ملفات CSV (*.csv)|*.csv";
                    saveDialog.DefaultExt = ".csv";
                    break;
                case ExportFormat.JSON:
                    saveDialog.Filter = "ملفات JSON (*.json)|*.json";
                    saveDialog.DefaultExt = ".json";
                    break;
                case ExportFormat.XML:
                    saveDialog.Filter = "ملفات XML (*.xml)|*.xml";
                    saveDialog.DefaultExt = ".xml";
                    break;
                case ExportFormat.PDF:
                    saveDialog.Filter = "ملفات HTML (*.html)|*.html";
                    saveDialog.DefaultExt = ".html";
                    break;
            }

            return saveDialog.ShowDialog() == true ? saveDialog.FileName : null;
        }
    }
}
