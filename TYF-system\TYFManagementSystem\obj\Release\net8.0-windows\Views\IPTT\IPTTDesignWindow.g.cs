﻿#pragma checksum "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BE829CD94E5F78B657B546650FD1ECB99E21F92F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace TYFManagementSystem.Views.IPTT {
    
    
    /// <summary>
    /// IPTTDesignWindow
    /// </summary>
    public partial class IPTTDesignWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 36 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectTitleText;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameText;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectTitle;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectManager;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectDuration;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectLocations;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LocationCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyLocationCountButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl LocationTabControl;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem MainTabItem;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid IpttDataGrid;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveIpttButton;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportExcelButton;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddIndicatorButton;
        
        #line default
        #line hidden
        
        
        #line 325 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveAllLocationsButton;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckDataButton;
        
        #line default
        #line hidden
        
        
        #line 447 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TYFManagementSystem;component/views/iptt/ipttdesignwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.ProjectNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ProjectTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ProjectManager = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ProjectDuration = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ProjectLocations = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.LocationCountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 102 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.LocationCountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.LocationCountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ApplyLocationCountButton = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.ApplyLocationCountButton.Click += new System.Windows.RoutedEventHandler(this.ApplyLocationCountButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.LocationTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 10:
            this.MainTabItem = ((System.Windows.Controls.TabItem)(target));
            return;
            case 11:
            this.IpttDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.SaveIpttButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.SaveIpttButton.Click += new System.Windows.RoutedEventHandler(this.SaveIpttButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ExportExcelButton = ((System.Windows.Controls.Button)(target));
            
            #line 258 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.ExportExcelButton.Click += new System.Windows.RoutedEventHandler(this.ExportExcelButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.AddIndicatorButton = ((System.Windows.Controls.Button)(target));
            
            #line 297 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.AddIndicatorButton.Click += new System.Windows.RoutedEventHandler(this.AddIndicatorButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 336 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SaveAllLocationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 378 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.SaveAllLocationsButton.Click += new System.Windows.RoutedEventHandler(this.SaveAllLocationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CheckDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 419 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.CheckDataButton.Click += new System.Windows.RoutedEventHandler(this.CheckDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 458 "..\..\..\..\..\Views\IPTT\IPTTDesignWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

