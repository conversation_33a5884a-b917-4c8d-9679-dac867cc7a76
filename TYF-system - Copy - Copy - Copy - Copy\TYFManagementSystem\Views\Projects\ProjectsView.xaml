<UserControl x:Class="TYFManagementSystem.Views.Projects.ProjectsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:TYFManagementSystem.ViewModels.Projects"
             mc:Ignorable="d"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:ProjectsViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <StackPanel>
                <TextBlock Text="إدارة المشاريع" 
                         FontSize="24" 
                         FontWeight="Bold" 
                         Foreground="#2E7D32"
                         HorizontalAlignment="Center"/>
                <TextBlock Text="إدارة وتتبع جميع مشاريع المؤسسة" 
                         FontSize="14" 
                         Foreground="#757575"
                         HorizontalAlignment="Center"
                         Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Search and Filter Section -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Search Box -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="البحث السريع:" VerticalAlignment="Center" FontWeight="Bold" Margin="0,0,10,0"/>
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                           Width="300"
                           Padding="10,8"
                           FontSize="14"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"
                           VerticalAlignment="Center"/>

                    <TextBlock Text="الحالة:" VerticalAlignment="Center" FontWeight="Bold" Margin="30,0,10,0"/>
                    <ComboBox ItemsSource="{Binding StatusFilters}"
                            SelectedItem="{Binding SelectedStatusFilter}"
                            Width="120"
                            Padding="10,8"
                            FontSize="14"
                            VerticalAlignment="Center"/>

                    <TextBlock Text="المنطقة:" VerticalAlignment="Center" FontWeight="Bold" Margin="20,0,10,0"/>
                    <ComboBox ItemsSource="{Binding RegionFilters}"
                            SelectedItem="{Binding SelectedRegionFilter}"
                            Width="120"
                            Padding="10,8"
                            FontSize="14"
                            VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="مشروع جديد"
                            Command="{Binding AddProjectCommand}"
                            Background="#2E7D32"
                            Foreground="White"
                            Padding="20,10"
                            Margin="8,0"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="5"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#4CAF50"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#1B5E20"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                    <Button Content="تعديل مشروع"
                            Command="{Binding EditProjectCommand}"
                            Background="#FF9800"
                            Foreground="White"
                            Padding="20,10"
                            Margin="8,0"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="5"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#FFB74D"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#E65100"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="حذف مشروع"
                            Command="{Binding DeleteProjectCommand}"
                            Background="#F44336"
                            Foreground="White"
                            Padding="20,10"
                            Margin="8,0"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="5"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#EF5350"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#C62828"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="تصدير إلى Excel"
                            Command="{Binding ExportToExcelCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="20,10"
                            Margin="8,0"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="5"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#66BB6A"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#388E3C"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="حفظ في قاعدة البيانات"
                            Command="{Binding SaveToDatabaseCommand}"
                            Background="#9C27B0"
                            Foreground="White"
                            Padding="20,10"
                            Margin="8,0"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="5"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#BA68C8"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#7B1FA2"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Projects DataGrid -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <DataGrid ItemsSource="{Binding ProjectsView}"
                      SelectedItem="{Binding SelectedProject}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      CanUserSortColumns="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      AlternatingRowBackground="#F9F9F9"
                      RowBackground="White"
                      FontSize="12"
                      FlowDirection="RightToLeft">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم التلقائي" Binding="{Binding Id}" Width="80" IsReadOnly="True" SortMemberPath="Id"/>
                    <DataGridTextColumn Header="اسم المشروع" Binding="{Binding Name}" Width="200" IsReadOnly="True" SortMemberPath="Name"/>
                    <DataGridTextColumn Header="منطقة المشروع" Binding="{Binding Region}" Width="120" IsReadOnly="True" SortMemberPath="Region"/>
                    <DataGridTextColumn Header="تاريخ البداية" Binding="{Binding StartDate, StringFormat=dd/MM/yyyy}" Width="120" IsReadOnly="True" SortMemberPath="StartDate"/>
                    <DataGridTextColumn Header="تاريخ النهاية" Binding="{Binding EndDate, StringFormat=dd/MM/yyyy}" Width="120" IsReadOnly="True" SortMemberPath="EndDate"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100" IsReadOnly="True" SortMemberPath="Status"/>

                    <!-- IPTT Button Column -->
                    <DataGridTemplateColumn Header="إدارة IPTT" Width="150" CanUserSort="False">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="تصميم/تحديث IPTT"
                                        Command="{Binding DataContext.DesignIPTTCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}"
                                        Background="#FF6B35"
                                        Foreground="White"
                                        Padding="8,4"
                                        Margin="2"
                                        FontSize="11"
                                        FontWeight="Bold"
                                        BorderThickness="0"
                                        Cursor="Hand">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="4"
                                                                Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#E55A2B"/>
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter Property="Background" Value="#CC4E24"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#2E7D32"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Padding" Value="10,8"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Height" Value="35"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E8F5E8"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#C8E6C9"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Border>
    </Grid>
</UserControl>
