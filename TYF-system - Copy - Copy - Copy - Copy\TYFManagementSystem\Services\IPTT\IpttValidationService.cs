using System.Collections.ObjectModel;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة التحقق من صحة بيانات IPTT
    /// </summary>
    public class IpttValidationService
    {
        /// <summary>
        /// نتيجة التحقق من صحة البيانات
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new List<string>();
            public List<string> Warnings { get; set; } = new List<string>();
            
            public bool HasErrors => Errors.Any();
            public bool HasWarnings => Warnings.Any();
        }

        /// <summary>
        /// التحقق من صحة بيانات المشروع
        /// </summary>
        public ValidationResult ValidateProject(Project project)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // التحقق من البيانات الأساسية
                if (project == null)
                {
                    result.Errors.Add("المشروع غير موجود");
                    result.IsValid = false;
                    return result;
                }

                if (string.IsNullOrWhiteSpace(project.Name))
                {
                    result.Errors.Add("اسم المشروع مطلوب");
                    result.IsValid = false;
                }

                if (project.StartDate >= project.EndDate)
                {
                    result.Errors.Add("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");
                    result.IsValid = false;
                }

                // التحقق من المدة الزمنية
                var duration = (project.EndDate - project.StartDate).Days;
                if (duration > 1095) // أكثر من 3 سنوات
                {
                    result.Warnings.Add("مدة المشروع طويلة جداً (أكثر من 3 سنوات)");
                }

                if (duration < 30) // أقل من شهر
                {
                    result.Warnings.Add("مدة المشروع قصيرة جداً (أقل من شهر)");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من المشروع: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة المؤشر
        /// </summary>
        public ValidationResult ValidateIndicator(IpttIndicator indicator)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (indicator == null)
                {
                    result.Errors.Add("المؤشر غير موجود");
                    result.IsValid = false;
                    return result;
                }

                // التحقق من رقم المؤشر
                if (string.IsNullOrWhiteSpace(indicator.No))
                {
                    result.Errors.Add("رقم المؤشر مطلوب");
                    result.IsValid = false;
                }

                // التحقق من اسم المؤشر
                if (string.IsNullOrWhiteSpace(indicator.Indicator))
                {
                    result.Errors.Add("اسم المؤشر مطلوب");
                    result.IsValid = false;
                }

                // التحقق من أنواع البيانات
                if (indicator.DataTypes == null || !indicator.DataTypes.Any())
                {
                    result.Errors.Add("يجب إضافة نوع بيانات واحد على الأقل للمؤشر");
                    result.IsValid = false;
                }
                else
                {
                    foreach (var dataType in indicator.DataTypes)
                    {
                        var dataTypeValidation = ValidateDataType(dataType);
                        if (!dataTypeValidation.IsValid)
                        {
                            result.Errors.AddRange(dataTypeValidation.Errors);
                            result.IsValid = false;
                        }
                        result.Warnings.AddRange(dataTypeValidation.Warnings);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من المؤشر: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة نوع البيانات
        /// </summary>
        public ValidationResult ValidateDataType(DataTypeItem dataType)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (dataType == null)
                {
                    result.Errors.Add("نوع البيانات غير موجود");
                    result.IsValid = false;
                    return result;
                }

                // التحقق من اسم نوع البيانات
                if (string.IsNullOrWhiteSpace(dataType.Name))
                {
                    result.Errors.Add("اسم نوع البيانات مطلوب");
                    result.IsValid = false;
                }

                // التحقق من الهدف
                if (string.IsNullOrWhiteSpace(dataType.Target))
                {
                    result.Warnings.Add($"لم يتم تحديد هدف لنوع البيانات '{dataType.Name}'");
                }
                else if (!double.TryParse(dataType.Target, out double target))
                {
                    result.Errors.Add($"الهدف لنوع البيانات '{dataType.Name}' يجب أن يكون رقماً صحيحاً");
                    result.IsValid = false;
                }
                else if (target < 0)
                {
                    result.Errors.Add($"الهدف لنوع البيانات '{dataType.Name}' لا يمكن أن يكون سالباً");
                    result.IsValid = false;
                }

                // التحقق من البيانات الشهرية
                if (dataType.MonthlyValues != null)
                {
                    foreach (var monthlyValue in dataType.MonthlyValues)
                    {
                        if (!string.IsNullOrWhiteSpace(monthlyValue.Value))
                        {
                            if (!double.TryParse(monthlyValue.Value, out double value))
                            {
                                result.Errors.Add($"القيمة الشهرية '{monthlyValue.Value}' في '{monthlyValue.Key}' يجب أن تكون رقماً");
                                result.IsValid = false;
                            }
                            else if (value < 0)
                            {
                                result.Warnings.Add($"القيمة الشهرية في '{monthlyValue.Key}' سالبة");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من نوع البيانات: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة بيانات الموقع
        /// </summary>
        public ValidationResult ValidateLocationData(
            ObservableCollection<IpttDisplayRow> locationRows,
            int locationId)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (locationRows == null || !locationRows.Any())
                {
                    result.Errors.Add($"لا توجد بيانات للموقع {locationId}");
                    result.IsValid = false;
                    return result;
                }

                // التحقق من صفوف أنواع البيانات
                var dataTypeRows = locationRows.Where(r => r.IsDataTypeRow).ToList();
                if (!dataTypeRows.Any())
                {
                    result.Errors.Add($"لا توجد أنواع بيانات في الموقع {locationId}");
                    result.IsValid = false;
                }

                foreach (var row in dataTypeRows)
                {
                    var rowValidation = ValidateDisplayRow(row);
                    if (!rowValidation.IsValid)
                    {
                        result.Errors.AddRange(rowValidation.Errors.Select(e => $"الموقع {locationId}: {e}"));
                        result.IsValid = false;
                    }
                    result.Warnings.AddRange(rowValidation.Warnings.Select(w => $"الموقع {locationId}: {w}"));
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من بيانات الموقع {locationId}: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة صف العرض
        /// </summary>
        public ValidationResult ValidateDisplayRow(IpttDisplayRow row)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (row == null)
                {
                    result.Errors.Add("صف البيانات غير موجود");
                    result.IsValid = false;
                    return result;
                }

                // التحقق من الهدف لصفوف أنواع البيانات
                if (row.IsDataTypeRow && !row.IsTotalRow)
                {
                    if (string.IsNullOrWhiteSpace(row.Target))
                    {
                        result.Warnings.Add($"لم يتم تحديد هدف لـ '{row.DataType}'");
                    }
                    else if (!double.TryParse(row.Target, out double target))
                    {
                        result.Errors.Add($"الهدف لـ '{row.DataType}' يجب أن يكون رقماً");
                        result.IsValid = false;
                    }
                    else if (target < 0)
                    {
                        result.Errors.Add($"الهدف لـ '{row.DataType}' لا يمكن أن يكون سالباً");
                        result.IsValid = false;
                    }
                }

                // التحقق من البيانات الشهرية
                if (row.MonthlyData != null)
                {
                    foreach (var monthData in row.MonthlyData)
                    {
                        if (!string.IsNullOrWhiteSpace(monthData.Value))
                        {
                            if (!double.TryParse(monthData.Value, out double value))
                            {
                                result.Errors.Add($"القيمة '{monthData.Value}' في '{monthData.Key}' يجب أن تكون رقماً");
                                result.IsValid = false;
                            }
                            else if (value < 0)
                            {
                                result.Warnings.Add($"القيمة في '{monthData.Key}' سالبة");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من صف البيانات: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// التحقق من تطابق البيانات بين المواقع
        /// </summary>
        public ValidationResult ValidateLocationConsistency(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                if (!locationData.Any())
                {
                    result.Errors.Add("لا توجد بيانات مواقع للتحقق منها");
                    result.IsValid = false;
                    return result;
                }

                // الحصول على الموقع الأول كمرجع
                var referenceLocation = locationData.Values.First();
                int expectedRowCount = referenceLocation.Count;

                // التحقق من تطابق عدد الصفوف
                foreach (var locationKvp in locationData)
                {
                    if (locationKvp.Value.Count != expectedRowCount)
                    {
                        result.Errors.Add($"عدد الصفوف في الموقع {locationKvp.Key} غير متطابق مع المواقع الأخرى");
                        result.IsValid = false;
                    }
                }

                // التحقق من تطابق هيكل البيانات
                foreach (var locationKvp in locationData)
                {
                    for (int i = 0; i < Math.Min(expectedRowCount, locationKvp.Value.Count); i++)
                    {
                        var referenceRow = referenceLocation[i];
                        var currentRow = locationKvp.Value[i];

                        if (referenceRow.IndicatorId != currentRow.IndicatorId ||
                            referenceRow.DataType != currentRow.DataType ||
                            referenceRow.IsMainIndicator != currentRow.IsMainIndicator ||
                            referenceRow.IsDataTypeRow != currentRow.IsDataTypeRow)
                        {
                            result.Errors.Add($"هيكل البيانات في الموقع {locationKvp.Key} غير متطابق مع المواقع الأخرى");
                            result.IsValid = false;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق من تطابق المواقع: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// التحقق من صحة عدد المواقع
        /// </summary>
        public ValidationResult ValidateLocationCount(int locationCount)
        {
            var result = new ValidationResult { IsValid = true };

            if (locationCount < 1)
            {
                result.Errors.Add("عدد المواقع يجب أن يكون 1 على الأقل");
                result.IsValid = false;
            }
            else if (locationCount > 20)
            {
                result.Errors.Add("عدد المواقع لا يمكن أن يتجاوز 20");
                result.IsValid = false;
            }
            else if (locationCount > 10)
            {
                result.Warnings.Add("عدد المواقع كبير، قد يؤثر على الأداء");
            }

            return result;
        }

        /// <summary>
        /// التحقق الشامل من بيانات IPTT
        /// </summary>
        public ValidationResult ValidateCompleteIpttData(IpttDataModel dataModel)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // التحقق من المشروع
                var projectValidation = ValidateProject(dataModel.ProjectInfo);
                result.Errors.AddRange(projectValidation.Errors);
                result.Warnings.AddRange(projectValidation.Warnings);
                if (!projectValidation.IsValid)
                    result.IsValid = false;

                // التحقق من المؤشرات
                foreach (var indicator in dataModel.Indicators)
                {
                    var indicatorValidation = ValidateIndicator(indicator);
                    result.Errors.AddRange(indicatorValidation.Errors);
                    result.Warnings.AddRange(indicatorValidation.Warnings);
                    if (!indicatorValidation.IsValid)
                        result.IsValid = false;
                }

                // التحقق من عدد المواقع
                var locationCountValidation = ValidateLocationCount(dataModel.LocationCount);
                result.Errors.AddRange(locationCountValidation.Errors);
                result.Warnings.AddRange(locationCountValidation.Warnings);
                if (!locationCountValidation.IsValid)
                    result.IsValid = false;

                // التحقق من بيانات المواقع
                foreach (var locationKvp in dataModel.LocationData)
                {
                    var locationValidation = ValidateLocationData(locationKvp.Value, locationKvp.Key);
                    result.Errors.AddRange(locationValidation.Errors);
                    result.Warnings.AddRange(locationValidation.Warnings);
                    if (!locationValidation.IsValid)
                        result.IsValid = false;
                }

                // التحقق من تطابق المواقع
                var consistencyValidation = ValidateLocationConsistency(dataModel.LocationData);
                result.Errors.AddRange(consistencyValidation.Errors);
                result.Warnings.AddRange(consistencyValidation.Warnings);
                if (!consistencyValidation.IsValid)
                    result.IsValid = false;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في التحقق الشامل من البيانات: {ex.Message}");
                result.IsValid = false;
            }

            return result;
        }
    }
}
