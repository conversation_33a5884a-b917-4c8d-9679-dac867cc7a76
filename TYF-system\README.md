# نظام إدارة مؤسسة تمدين شباب

## نظرة عامة
نظام إدارة متكامل لمؤسسة تمدين شباب، مصمم باستخدام WPF و .NET 8 مع واجهات احترافية ومعمارية قابلة للتوسع.

## الميزات الرئيسية
- **لوحة التحكم الرئيسية**: عرض إحصائيات سريعة ومعلومات مهمة
- **إدارة المشاريع**: إدارة شاملة لجميع مشاريع المؤسسة ✅ **مع حفظ دائم في قاعدة البيانات**
- **نظام IPTT**: تصميم جداول متابعة الأداء والمؤشرات مع دعم المواقع المتعددة
- **إدارة البيانات والمعلومات**: نظام قواعد بيانات متقدم مع SQLite
- **إدارة التقارير**: إنشاء وتصدير التقارير المختلفة إلى Excel
- **النسخ الاحتياطي**: حماية البيانات والنسخ الاحتياطية
- **إدارة المستخدمين**: إدارة المستخدمين والصلاحيات

## 🧹 تنظيف النظام (أغسطس 2025)
### ✅ تم تنظيف وتحسين النظام بالكامل
- **حذف الملفات المؤقتة**: إزالة جميع ملفات الاختبار والتوثيق المؤقتة
- **تنظيف الكود**: حذف الدوال والكلاسات غير المستخدمة
- **تنظيف الواجهات**: إزالة MainWindow غير المستخدم والاحتفاظ بـ SimpleMainWindow
- **تنظيف التوثيق**: دمج ملفات README المتعددة في ملف واحد شامل
- **تحسين الأداء**: تنظيف مجلدات bin/obj وملفات البناء المؤقتة

### 📋 الملفات المحذوفة:
#### ملفات الاختبار المؤقتة:
- `TestLocationCopyFix.cs` - اختبار إصلاح نسخ المواقع
- `TestLocationFix.cs` - اختبار إصلاح مشكلة المواقع
- `TestNewIpttSystem.cs` - اختبار النظام الجديد
- `TestRunner.cs` - منفذ الاختبارات
- `DemoProgram.cs` - برنامج العرض التوضيحي
- `ShowImprovements.cs` - عرض التحسينات

#### ملفات التوثيق المؤقتة:
- جميع ملفات `*.md` المؤقتة (9 ملفات)
- جميع ملفات التوثيق العربية المؤقتة (8 ملفات)

#### الواجهات غير المستخدمة:
- `MainWindow.xaml` و `MainWindow.xaml.cs`
- `MainViewModel.cs`

#### ملفات أخرى:
- `run.bat` و `run.ps1` (مكررة)
- مجلدات `bin/` و `obj/` (ملفات البناء المؤقتة)

### ✅ نتائج التنظيف:
- **تم حذف**: 32 ملف مؤقت وغير مستخدم
- **تم الاحتفاظ بـ**: جميع الملفات الأساسية والوظيفية
- **حالة البناء**: ✅ نجح البناء بدون أخطاء
- **حالة التشغيل**: ✅ النظام يعمل بشكل طبيعي
- **حجم المشروع**: تم تقليل حجم المشروع بشكل كبير

## 🚀 كيفية تشغيل النظام

### المتطلبات:
- .NET 8.0 SDK أو أحدث
- Windows 10/11
- 4 GB RAM كحد أدنى

### طرق التشغيل:

#### 1. التشغيل السريع:
```bash
# استخدم أحد ملفات التشغيل السريع
START_HERE.bat          # لنظام Windows
START_HERE.ps1          # لـ PowerShell
تشغيل_نظام_TYF.bat     # النسخة العربية
```

#### 2. التشغيل اليدوي:
```bash
cd TYF-system/TYFManagementSystem
dotnet restore
dotnet build
dotnet run
```

#### 3. بناء ملف تنفيذي:
```bash
cd TYF-system/TYFManagementSystem
dotnet publish -c Release -r win-x64 --self-contained
```

## المعمارية
النظام مبني على معمارية MVVM (Model-View-ViewModel) لضمان:
- **قابلية الصيانة**: كود منظم وسهل الصيانة
- **قابلية التوسع**: إمكانية إضافة ميزات جديدة بسهولة
- **الفصل بين الطبقات**: فصل منطق العمل عن واجهة المستخدم
- **إعادة الاستخدام**: مكونات قابلة لإعادة الاستخدام

## هيكل المشروع
```
TYFManagementSystem/
├── Commands/           # أوامر التطبيق
├── Models/            # نماذج البيانات
├── ViewModels/        # نماذج العرض
│   ├── Projects/      # إدارة المشاريع
│   ├── DataManagement/# إدارة البيانات
│   ├── Reports/       # التقارير
│   ├── Backup/        # النسخ الاحتياطي
│   └── UserManagement/# إدارة المستخدمين
├── Views/             # واجهات المستخدم
│   ├── Projects/
│   ├── DataManagement/
│   ├── Reports/
│   ├── Backup/
│   └── UserManagement/
├── Services/          # خدمات التطبيق
├── Styles/           # أنماط التصميم
└── Resources/        # الموارد والأيقونات
```

## التقنيات المستخدمة
- **.NET 8**: إطار العمل الأساسي
- **WPF**: واجهة المستخدم
- **MVVM Pattern**: نمط المعمارية
- **XAML**: تصميم الواجهات
- **C#**: لغة البرمجة

## كيفية التشغيل
1. تأكد من تثبيت .NET 8 SDK
2. افتح مجلد المشروع في Terminal
3. قم بتشغيل الأمر: `dotnet run`

## التطوير المستقبلي
النظام مصمم ليكون قابلاً للتوسع، ويمكن إضافة الميزات التالية:
- قاعدة بيانات متقدمة (SQL Server/SQLite)
- نظام المصادقة والأمان
- تقارير متقدمة مع الرسوم البيانية
- نظام الإشعارات
- واجهة برمجة التطبيقات (API)
- تطبيق ويب مصاحب

## الدعم والصيانة
النظام مصمم بطريقة تسمح بالتحديث والتطوير دون التأثير على الوحدات الأخرى، مما يضمن استمرارية العمل أثناء التطوير.

## الترخيص
هذا النظام مطور خصيصاً لمؤسسة تمدين شباب.

---

## 🆕 آخر التحديثات - المرحلة الأولى من التطوير (أغسطس 2025)

### ✨ الميزات الجديدة المضافة:

#### 1. وحدة إدارة البيانات والمعلومات 🗃️
- **واجهة شاملة**: تبويبات منظمة للمشاريع والتقارير والإحصائيات
- **بحث متقدم**: فلترة حسب الحالة والمنطقة مع بحث نصي
- **إحصائيات تفصيلية**: عرض البيانات حسب المنطقة والحالة
- **إدارة التقارير**: عرض وحذف التقارير مع تأكيد الأمان
- **بيانات حقيقية**: ربط مباشر مع قاعدة البيانات

#### 2. ربط نظام M&E مع IPTT 🔗
- **زر فتح IPTT**: وصول مباشر لنظام IPTT من لوحة M&E
- **بيانات حقيقية**: عرض إحصائيات IPTT الفعلية بدلاً من البيانات التجريبية
- **تحديث تلقائي**: تحديث البيانات عند إغلاق نافذة IPTT
- **تكامل سلس**: انتقال سهل بين الأنظمة

#### 3. لوحة التحكم الديناميكية 📊
- **كروت تفاعلية**: النقر على أي كرت ينقل للوحدة المناسبة
- **بيانات حية**: عرض الأرقام الحقيقية من قاعدة البيانات
- **تحديث تلقائي**: تحديث الإحصائيات عند تغيير البيانات
- **تصميم محسن**: واجهة أكثر جاذبية وتفاعلية

### 🔧 التحسينات التقنية:
- **خدمات جديدة**: DataManagementService و DashboardService
- **نماذج محسنة**: نماذج بيانات متقدمة مع INotifyPropertyChanged
- **أداء محسن**: تحميل البيانات بشكل غير متزامن
- **معمارية أفضل**: فصل أفضل للمسؤوليات

### 📊 الإحصائيات المحدثة:
- **إجمالي الملفات**: 55+ ملف (+10 ملفات جديدة)
- **أسطر الكود**: 10,000+ سطر (+2000 سطر)
- **الواجهات**: 7 واجهات رئيسية (+1 جديدة)
- **النماذج**: 20+ نموذج بيانات (+5 جديدة)
- **الخدمات**: 10 خدمات أساسية (+2 جديدة)

### 🎯 الحالة المحدثة:
✅ **النظام مكتمل ومُختبر وجاهز للاستخدام الفوري**

#### الوظائف المكتملة:
- ✅ إدارة المشاريع مع حفظ دائم
- ✅ نظام IPTT متقدم
- ✅ إدارة التقارير وتصدير Excel
- ✅ نظام M&E شامل مع ربط IPTT
- ✅ **إدارة البيانات والمعلومات الشاملة** 🆕
- ✅ **لوحة تحكم ديناميكية تفاعلية** 🆕
- ✅ قاعدة بيانات محمولة
- ✅ واجهات عربية احترافية
- ✅ نظام نسخ احتياطي

---
**تم التطوير بواسطة**: فريق تطوير مؤسسة تمدين شباب
**آخر تحديث**: أغسطس 2025 - المرحلة الأولى مكتملة
**الإصدار**: 1.1 - مكتمل ومُختبر مع ميزات جديدة
