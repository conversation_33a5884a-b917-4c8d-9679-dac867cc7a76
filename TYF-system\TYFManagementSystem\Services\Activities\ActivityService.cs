using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models.Activities;

namespace TYFManagementSystem.Services.Activities
{
    /// <summary>
    /// خدمة إدارة الأنشطة الميدانية
    /// </summary>
    public class ActivityService : IDisposable
    {
        private readonly TyfDbContext _context;

        public ActivityService()
        {
            _context = new TyfDbContext();
        }

        /// <summary>
        /// الحصول على جميع الأنشطة مع الفلاتر
        /// </summary>
        public async Task<List<ActivitySummary>> GetActivitiesAsync(ActivitySearchFilters? filters = null)
        {
            try
            {
                var query = _context.FieldActivities
                    .Include(a => a.LinkedIndicators)
                    .Include(a => a.Documents)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (filters != null)
                {
                    if (!string.IsNullOrEmpty(filters.SearchText))
                    {
                        query = query.Where(a => a.ActivityName.Contains(filters.SearchText) ||
                                               a.Description.Contains(filters.SearchText) ||
                                               a.Location.Contains(filters.SearchText));
                    }

                    if (!string.IsNullOrEmpty(filters.ProjectId))
                    {
                        query = query.Where(a => a.ProjectId == filters.ProjectId);
                    }

                    if (!string.IsNullOrEmpty(filters.Location))
                    {
                        query = query.Where(a => a.Location.Contains(filters.Location));
                    }

                    if (filters.Status.HasValue)
                    {
                        query = query.Where(a => a.Status == filters.Status.Value);
                    }

                    if (filters.Priority.HasValue)
                    {
                        query = query.Where(a => a.Priority == filters.Priority.Value);
                    }

                    if (filters.StartDateFrom.HasValue)
                    {
                        query = query.Where(a => a.StartDate >= filters.StartDateFrom.Value);
                    }

                    if (filters.StartDateTo.HasValue)
                    {
                        query = query.Where(a => a.StartDate <= filters.StartDateTo.Value);
                    }

                    if (!string.IsNullOrEmpty(filters.ResponsiblePerson))
                    {
                        query = query.Where(a => a.ResponsiblePerson.Contains(filters.ResponsiblePerson));
                    }

                    if (filters.HasLinkedIndicators.HasValue)
                    {
                        if (filters.HasLinkedIndicators.Value)
                            query = query.Where(a => a.LinkedIndicators.Any());
                        else
                            query = query.Where(a => !a.LinkedIndicators.Any());
                    }

                    if (filters.HasDocuments.HasValue)
                    {
                        if (filters.HasDocuments.Value)
                            query = query.Where(a => a.Documents.Any());
                        else
                            query = query.Where(a => !a.Documents.Any());
                    }
                }

                var activities = await query.OrderBy(a => a.StartDate).ToListAsync();

                // تحويل إلى ملخص الأنشطة
                var summaries = new List<ActivitySummary>();
                foreach (var activity in activities)
                {
                    var projectName = await GetProjectNameAsync(activity.ProjectId);
                    
                    summaries.Add(new ActivitySummary
                    {
                        ActivityId = activity.ActivityId,
                        ActivityName = activity.ActivityName,
                        ProjectName = projectName,
                        Location = activity.Location,
                        StartDate = activity.StartDate,
                        EndDate = activity.EndDate,
                        Status = activity.Status,
                        Priority = activity.Priority,
                        ResponsiblePerson = activity.ResponsiblePerson,
                        LinkedIndicatorsCount = activity.LinkedIndicators.Count,
                        DocumentsCount = activity.Documents.Count,
                        ProgressPercentage = CalculateProgressPercentage(activity),
                        StatusText = GetStatusText(activity.Status),
                        PriorityText = GetPriorityText(activity.Priority)
                    });
                }

                return summaries;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الأنشطة: {ex.Message}");
                return new List<ActivitySummary>();
            }
        }

        /// <summary>
        /// الحصول على نشاط محدد
        /// </summary>
        public async Task<FieldActivity?> GetActivityAsync(string activityId)
        {
            try
            {
                return await _context.FieldActivities
                    .Include(a => a.LinkedIndicators)
                    .Include(a => a.Documents)
                    .Include(a => a.ActivityResults)
                    .FirstOrDefaultAsync(a => a.ActivityId == activityId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل النشاط: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء نشاط جديد
        /// </summary>
        public async Task<bool> CreateActivityAsync(FieldActivity activity)
        {
            try
            {
                activity.ActivityId = Guid.NewGuid().ToString();
                activity.CreatedDate = DateTime.Now;
                activity.LastModified = DateTime.Now;

                _context.FieldActivities.Add(activity);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النشاط: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث نشاط موجود
        /// </summary>
        public async Task<bool> UpdateActivityAsync(FieldActivity activity)
        {
            try
            {
                var existingActivity = await _context.FieldActivities.FindAsync(activity.ActivityId);
                if (existingActivity == null) return false;

                // تحديث الخصائص
                existingActivity.ActivityName = activity.ActivityName;
                existingActivity.Description = activity.Description;
                existingActivity.Objectives = activity.Objectives;
                existingActivity.ProjectId = activity.ProjectId;
                existingActivity.Location = activity.Location;
                existingActivity.LocationDetails = activity.LocationDetails;
                existingActivity.Latitude = activity.Latitude;
                existingActivity.Longitude = activity.Longitude;
                existingActivity.StartDate = activity.StartDate;
                existingActivity.EndDate = activity.EndDate;
                existingActivity.Status = activity.Status;
                existingActivity.Priority = activity.Priority;
                existingActivity.ResponsiblePerson = activity.ResponsiblePerson;
                existingActivity.ContactInfo = activity.ContactInfo;
                existingActivity.EstimatedParticipants = activity.EstimatedParticipants;
                existingActivity.ActualParticipants = activity.ActualParticipants;
                existingActivity.EstimatedBudget = activity.EstimatedBudget;
                existingActivity.ActualBudget = activity.ActualBudget;
                existingActivity.Notes = activity.Notes;
                existingActivity.Results = activity.Results;
                existingActivity.LastModified = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث النشاط: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف نشاط
        /// </summary>
        public async Task<bool> DeleteActivityAsync(string activityId)
        {
            try
            {
                var activity = await _context.FieldActivities.FindAsync(activityId);
                if (activity == null) return false;

                _context.FieldActivities.Remove(activity);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف النشاط: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على بيانات التقويم
        /// </summary>
        public async Task<List<CalendarActivityData>> GetCalendarDataAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var activities = await _context.FieldActivities
                    .Where(a => a.StartDate <= endDate && a.EndDate >= startDate)
                    .ToListAsync();

                var calendarData = new List<CalendarActivityData>();
                foreach (var activity in activities)
                {
                    var projectName = await GetProjectNameAsync(activity.ProjectId);
                    
                    calendarData.Add(new CalendarActivityData
                    {
                        ActivityId = activity.ActivityId,
                        ActivityName = activity.ActivityName,
                        ProjectName = projectName,
                        StartDate = activity.StartDate,
                        EndDate = activity.EndDate,
                        Status = activity.Status,
                        Priority = activity.Priority,
                        Location = activity.Location,
                        ResponsiblePerson = activity.ResponsiblePerson,
                        StatusColor = GetStatusColor(activity.Status),
                        PriorityColor = GetPriorityColor(activity.Priority)
                    });
                }

                return calendarData.OrderBy(c => c.StartDate).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات التقويم: {ex.Message}");
                return new List<CalendarActivityData>();
            }
        }

        /// <summary>
        /// ربط نشاط بمؤشر
        /// </summary>
        public async Task<bool> LinkActivityToIndicatorAsync(string activityId, string indicatorId, double expectedContribution)
        {
            try
            {
                var existingLink = await _context.ActivityIndicatorLinks
                    .FirstOrDefaultAsync(l => l.ActivityId == activityId && l.IndicatorId == indicatorId);

                if (existingLink != null) return false; // الربط موجود بالفعل

                var indicatorName = await GetIndicatorNameAsync(indicatorId);
                
                var link = new ActivityIndicatorLink
                {
                    ActivityId = activityId,
                    IndicatorId = indicatorId,
                    IndicatorName = indicatorName,
                    ExpectedContribution = expectedContribution,
                    CreatedDate = DateTime.Now
                };

                _context.ActivityIndicatorLinks.Add(link);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ربط النشاط بالمؤشر: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إلغاء ربط نشاط بمؤشر
        /// </summary>
        public async Task<bool> UnlinkActivityFromIndicatorAsync(string activityId, string indicatorId)
        {
            try
            {
                var link = await _context.ActivityIndicatorLinks
                    .FirstOrDefaultAsync(l => l.ActivityId == activityId && l.IndicatorId == indicatorId);

                if (link == null) return false;

                _context.ActivityIndicatorLinks.Remove(link);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إلغاء ربط النشاط بالمؤشر: {ex.Message}");
                return false;
            }
        }

        #region Helper Methods

        private async Task<string> GetProjectNameAsync(string projectId)
        {
            try
            {
                var project = await _context.IpttProjects.FirstOrDefaultAsync(p => p.ProjectId == projectId);
                return project?.ProjectName ?? "مشروع غير محدد";
            }
            catch
            {
                return "مشروع غير محدد";
            }
        }

        private async Task<string> GetIndicatorNameAsync(string indicatorId)
        {
            try
            {
                var indicator = await _context.IpttIndicators.FirstOrDefaultAsync(i => i.Id.ToString() == indicatorId);
                return indicator?.IndicatorName ?? "مؤشر غير محدد";
            }
            catch
            {
                return "مؤشر غير محدد";
            }
        }

        private double CalculateProgressPercentage(FieldActivity activity)
        {
            if (activity.Status == ActivityStatus.Completed) return 100;
            if (activity.Status == ActivityStatus.Cancelled) return 0;

            var totalDays = (activity.EndDate - activity.StartDate).TotalDays;
            if (totalDays <= 0) return 0;

            var elapsedDays = (DateTime.Now - activity.StartDate).TotalDays;
            if (elapsedDays <= 0) return 0;
            if (elapsedDays >= totalDays) return activity.Status == ActivityStatus.InProgress ? 90 : 100;

            return Math.Min(90, (elapsedDays / totalDays) * 100);
        }

        private string GetStatusText(ActivityStatus status)
        {
            return status switch
            {
                ActivityStatus.Planned => "مخطط",
                ActivityStatus.InProgress => "جاري التنفيذ",
                ActivityStatus.Completed => "مكتمل",
                ActivityStatus.Cancelled => "ملغي",
                ActivityStatus.Postponed => "مؤجل",
                ActivityStatus.OnHold => "متوقف مؤقتاً",
                _ => "غير محدد"
            };
        }

        private string GetPriorityText(ActivityPriority priority)
        {
            return priority switch
            {
                ActivityPriority.Low => "منخفضة",
                ActivityPriority.Medium => "متوسطة",
                ActivityPriority.High => "عالية",
                ActivityPriority.Critical => "حرجة",
                _ => "غير محدد"
            };
        }

        private string GetStatusColor(ActivityStatus status)
        {
            return status switch
            {
                ActivityStatus.Planned => "#2196F3",      // أزرق
                ActivityStatus.InProgress => "#FF9800",   // برتقالي
                ActivityStatus.Completed => "#4CAF50",    // أخضر
                ActivityStatus.Cancelled => "#F44336",    // أحمر
                ActivityStatus.Postponed => "#9C27B0",    // بنفسجي
                ActivityStatus.OnHold => "#607D8B",       // رمادي
                _ => "#757575"
            };
        }

        private string GetPriorityColor(ActivityPriority priority)
        {
            return priority switch
            {
                ActivityPriority.Low => "#4CAF50",        // أخضر
                ActivityPriority.Medium => "#FF9800",     // برتقالي
                ActivityPriority.High => "#F44336",       // أحمر
                ActivityPriority.Critical => "#9C27B0",   // بنفسجي
                _ => "#757575"
            };
        }

        #endregion

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
