{
  "version": 3,
  "targets": {
    "net8.0-windows7.0": {
      "DocumentFormat.OpenXml/3.0.2": {
        "type": "package",
        "dependencies": {
          "DocumentFormat.OpenXml.Framework": "3.0.2"
        },
        "compile": {
          "lib/net8.0/DocumentFormat.OpenXml.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/DocumentFormat.OpenXml.dll": {
            "related": ".xml"
          }
        }
      },
      "DocumentFormat.OpenXml.Framework/3.0.2": {
        "type": "package",
        "dependencies": {
          "System.IO.Packaging": "8.0.0"
        },
        "compile": {
          "lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {
            "related": ".xml"
          }
        }
      },
      "EPPlus/7.0.0": {
        "type": "package",
        "dependencies": {
          "EPPlus.Interfaces": "6.1.1",
          "EPPlus.System.Drawing": "6.1.1",
          "Microsoft.Extensions.Configuration.Json": "7.0.0",
          "Microsoft.IO.RecyclableMemoryStream": "2.3.2",
          "System.ComponentModel.Annotations": "5.0.0",
          "System.Security.Cryptography.Pkcs": "7.0.2",
          "System.Text.Encoding.CodePages": "7.0.0"
        },
        "compile": {
          "lib/net7.0/EPPlus.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/EPPlus.dll": {
            "related": ".xml"
          }
        }
      },
      "EPPlus.Interfaces/6.1.1": {
        "type": "package",
        "compile": {
          "lib/net7.0/EPPlus.Interfaces.dll": {}
        },
        "runtime": {
          "lib/net7.0/EPPlus.Interfaces.dll": {}
        }
      },
      "EPPlus.System.Drawing/6.1.1": {
        "type": "package",
        "dependencies": {
          "EPPlus.Interfaces": "6.1.1",
          "System.Drawing.Common": "7.0.0"
        },
        "compile": {
          "lib/net7.0/EPPlus.System.Drawing.dll": {}
        },
        "runtime": {
          "lib/net7.0/EPPlus.System.Drawing.dll": {}
        }
      },
      "Humanizer.Core/2.14.1": {
        "type": "package",
        "compile": {
          "lib/net6.0/Humanizer.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/Humanizer.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.Bcl.AsyncInterfaces/6.0.0": {
        "type": "package",
        "compile": {
          "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.CodeAnalysis.Analyzers/3.3.3": {
        "type": "package",
        "build": {
          "build/_._": {}
        }
      },
      "Microsoft.CodeAnalysis.Common/4.5.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.CodeAnalysis.Analyzers": "3.3.3",
          "System.Collections.Immutable": "6.0.0",
          "System.Reflection.Metadata": "6.0.1",
          "System.Runtime.CompilerServices.Unsafe": "6.0.0",
          "System.Text.Encoding.CodePages": "6.0.0"
        },
        "compile": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {
            "related": ".pdb;.xml"
          }
        },
        "runtime": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {
            "related": ".pdb;.xml"
          }
        },
        "resource": {
          "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "cs"
          },
          "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "de"
          },
          "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "es"
          },
          "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "fr"
          },
          "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "it"
          },
          "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "ja"
          },
          "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "ko"
          },
          "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "pl"
          },
          "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "pt-BR"
          },
          "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "ru"
          },
          "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "tr"
          },
          "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "zh-Hans"
          },
          "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {
            "locale": "zh-Hant"
          }
        }
      },
      "Microsoft.CodeAnalysis.CSharp/4.5.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.CodeAnalysis.Common": "[4.5.0]"
        },
        "compile": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {
            "related": ".pdb;.xml"
          }
        },
        "runtime": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {
            "related": ".pdb;.xml"
          }
        },
        "resource": {
          "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "cs"
          },
          "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "de"
          },
          "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "es"
          },
          "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "fr"
          },
          "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "it"
          },
          "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "ja"
          },
          "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "ko"
          },
          "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "pl"
          },
          "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "pt-BR"
          },
          "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "ru"
          },
          "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "tr"
          },
          "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "zh-Hans"
          },
          "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {
            "locale": "zh-Hant"
          }
        }
      },
      "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {
        "type": "package",
        "dependencies": {
          "Humanizer.Core": "2.14.1",
          "Microsoft.CodeAnalysis.CSharp": "[4.5.0]",
          "Microsoft.CodeAnalysis.Common": "[4.5.0]",
          "Microsoft.CodeAnalysis.Workspaces.Common": "[4.5.0]"
        },
        "compile": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {
            "related": ".pdb;.xml"
          }
        },
        "runtime": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {
            "related": ".pdb;.xml"
          }
        },
        "resource": {
          "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "cs"
          },
          "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "de"
          },
          "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "es"
          },
          "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "fr"
          },
          "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "it"
          },
          "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "ja"
          },
          "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "ko"
          },
          "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "pl"
          },
          "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "pt-BR"
          },
          "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "ru"
          },
          "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "tr"
          },
          "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "zh-Hans"
          },
          "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {
            "locale": "zh-Hant"
          }
        }
      },
      "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {
        "type": "package",
        "dependencies": {
          "Humanizer.Core": "2.14.1",
          "Microsoft.Bcl.AsyncInterfaces": "6.0.0",
          "Microsoft.CodeAnalysis.Common": "[4.5.0]",
          "System.Composition": "6.0.0",
          "System.IO.Pipelines": "6.0.3",
          "System.Threading.Channels": "6.0.0"
        },
        "compile": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {
            "related": ".pdb;.xml"
          }
        },
        "runtime": {
          "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {
            "related": ".pdb;.xml"
          }
        },
        "resource": {
          "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "cs"
          },
          "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "de"
          },
          "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "es"
          },
          "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "fr"
          },
          "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "it"
          },
          "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "ja"
          },
          "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "ko"
          },
          "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "pl"
          },
          "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "pt-BR"
          },
          "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "ru"
          },
          "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "tr"
          },
          "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "zh-Hans"
          },
          "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {
            "locale": "zh-Hant"
          }
        }
      },
      "Microsoft.Data.Sqlite.Core/8.0.0": {
        "type": "package",
        "dependencies": {
          "SQLitePCLRaw.core": "2.1.6"
        },
        "compile": {
          "lib/net8.0/Microsoft.Data.Sqlite.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Data.Sqlite.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.EntityFrameworkCore/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.EntityFrameworkCore.Abstractions": "8.0.0",
          "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0",
          "Microsoft.Extensions.Caching.Memory": "8.0.0",
          "Microsoft.Extensions.Logging": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}
        }
      },
      "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {
        "type": "package",
        "compile": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {
        "type": "package",
        "compile": {
          "lib/netstandard2.0/_._": {}
        },
        "runtime": {
          "lib/netstandard2.0/_._": {}
        }
      },
      "Microsoft.EntityFrameworkCore.Design/8.0.0": {
        "type": "package",
        "dependencies": {
          "Humanizer.Core": "2.14.1",
          "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0",
          "Microsoft.EntityFrameworkCore.Relational": "8.0.0",
          "Microsoft.Extensions.DependencyModel": "8.0.0",
          "Mono.TextTemplating": "2.2.1"
        },
        "compile": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}
        }
      },
      "Microsoft.EntityFrameworkCore.Relational/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.EntityFrameworkCore": "8.0.0",
          "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.EntityFrameworkCore.Sqlite/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.EntityFrameworkCore.Sqlite.Core": "8.0.0",
          "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"
        },
        "compile": {
          "lib/net8.0/_._": {}
        },
        "runtime": {
          "lib/net8.0/_._": {}
        }
      },
      "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Data.Sqlite.Core": "8.0.0",
          "Microsoft.EntityFrameworkCore.Relational": "8.0.0",
          "Microsoft.Extensions.DependencyModel": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.EntityFrameworkCore.Tools/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.EntityFrameworkCore.Design": "8.0.0"
        },
        "compile": {
          "lib/net8.0/_._": {}
        },
        "runtime": {
          "lib/net8.0/_._": {}
        }
      },
      "Microsoft.Extensions.Caching.Abstractions/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Primitives": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Caching.Memory/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Caching.Abstractions": "8.0.0",
          "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0",
          "Microsoft.Extensions.Logging.Abstractions": "8.0.0",
          "Microsoft.Extensions.Options": "8.0.0",
          "Microsoft.Extensions.Primitives": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Configuration/7.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Configuration.Abstractions": "7.0.0",
          "Microsoft.Extensions.Primitives": "7.0.0"
        },
        "compile": {
          "lib/net7.0/Microsoft.Extensions.Configuration.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Extensions.Configuration.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Primitives": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Configuration": "7.0.0",
          "Microsoft.Extensions.Configuration.Abstractions": "7.0.0",
          "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0",
          "Microsoft.Extensions.FileProviders.Physical": "7.0.0",
          "Microsoft.Extensions.Primitives": "7.0.0"
        },
        "compile": {
          "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Configuration.Json/7.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Configuration": "7.0.0",
          "Microsoft.Extensions.Configuration.Abstractions": "7.0.0",
          "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0",
          "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0",
          "System.Text.Json": "7.0.0"
        },
        "compile": {
          "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.DependencyInjection/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {
        "type": "package",
        "compile": {
          "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.DependencyModel/8.0.0": {
        "type": "package",
        "dependencies": {
          "System.Text.Encodings.Web": "8.0.0",
          "System.Text.Json": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.Primitives": "7.0.0"
        },
        "compile": {
          "lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.FileProviders.Physical/7.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0",
          "Microsoft.Extensions.FileSystemGlobbing": "7.0.0",
          "Microsoft.Extensions.Primitives": "7.0.0"
        },
        "compile": {
          "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {
        "type": "package",
        "compile": {
          "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Logging/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.DependencyInjection": "8.0.0",
          "Microsoft.Extensions.Logging.Abstractions": "8.0.0",
          "Microsoft.Extensions.Options": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Logging.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Logging.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.Extensions.Logging.Abstractions/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}
        }
      },
      "Microsoft.Extensions.Options/8.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0",
          "Microsoft.Extensions.Primitives": "8.0.0"
        },
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Options.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Options.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}
        }
      },
      "Microsoft.Extensions.Primitives/8.0.0": {
        "type": "package",
        "compile": {
          "lib/net8.0/Microsoft.Extensions.Primitives.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/Microsoft.Extensions.Primitives.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "Microsoft.IO.RecyclableMemoryStream/2.3.2": {
        "type": "package",
        "compile": {
          "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {
            "related": ".xml"
          }
        }
      },
      "Microsoft.Win32.SystemEvents/7.0.0": {
        "type": "package",
        "compile": {
          "lib/net7.0/Microsoft.Win32.SystemEvents.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/Microsoft.Win32.SystemEvents.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        },
        "runtimeTargets": {
          "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {
            "assetType": "runtime",
            "rid": "win"
          }
        }
      },
      "Mono.TextTemplating/2.2.1": {
        "type": "package",
        "dependencies": {
          "System.CodeDom": "4.4.0"
        },
        "compile": {
          "lib/netstandard2.0/Mono.TextTemplating.dll": {}
        },
        "runtime": {
          "lib/netstandard2.0/Mono.TextTemplating.dll": {}
        }
      },
      "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {
        "type": "package",
        "dependencies": {
          "SQLitePCLRaw.lib.e_sqlite3": "2.1.6",
          "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"
        },
        "compile": {
          "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}
        },
        "runtime": {
          "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}
        }
      },
      "SQLitePCLRaw.core/2.1.6": {
        "type": "package",
        "dependencies": {
          "System.Memory": "4.5.3"
        },
        "compile": {
          "lib/netstandard2.0/SQLitePCLRaw.core.dll": {}
        },
        "runtime": {
          "lib/netstandard2.0/SQLitePCLRaw.core.dll": {}
        }
      },
      "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {
        "type": "package",
        "compile": {
          "lib/netstandard2.0/_._": {}
        },
        "runtime": {
          "lib/netstandard2.0/_._": {}
        },
        "build": {
          "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}
        },
        "runtimeTargets": {
          "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {
            "assetType": "native",
            "rid": "browser-wasm"
          },
          "runtimes/linux-arm/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-arm"
          },
          "runtimes/linux-arm64/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-arm64"
          },
          "runtimes/linux-armel/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-armel"
          },
          "runtimes/linux-mips64/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-mips64"
          },
          "runtimes/linux-musl-arm/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-musl-arm"
          },
          "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-musl-arm64"
          },
          "runtimes/linux-musl-x64/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-musl-x64"
          },
          "runtimes/linux-ppc64le/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-ppc64le"
          },
          "runtimes/linux-s390x/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-s390x"
          },
          "runtimes/linux-x64/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-x64"
          },
          "runtimes/linux-x86/native/libe_sqlite3.so": {
            "assetType": "native",
            "rid": "linux-x86"
          },
          "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {
            "assetType": "native",
            "rid": "maccatalyst-arm64"
          },
          "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {
            "assetType": "native",
            "rid": "maccatalyst-x64"
          },
          "runtimes/osx-arm64/native/libe_sqlite3.dylib": {
            "assetType": "native",
            "rid": "osx-arm64"
          },
          "runtimes/osx-x64/native/libe_sqlite3.dylib": {
            "assetType": "native",
            "rid": "osx-x64"
          },
          "runtimes/win-arm/native/e_sqlite3.dll": {
            "assetType": "native",
            "rid": "win-arm"
          },
          "runtimes/win-arm64/native/e_sqlite3.dll": {
            "assetType": "native",
            "rid": "win-arm64"
          },
          "runtimes/win-x64/native/e_sqlite3.dll": {
            "assetType": "native",
            "rid": "win-x64"
          },
          "runtimes/win-x86/native/e_sqlite3.dll": {
            "assetType": "native",
            "rid": "win-x86"
          }
        }
      },
      "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {
        "type": "package",
        "dependencies": {
          "SQLitePCLRaw.core": "2.1.6"
        },
        "compile": {
          "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}
        },
        "runtime": {
          "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}
        }
      },
      "System.CodeDom/4.4.0": {
        "type": "package",
        "compile": {
          "ref/netstandard2.0/System.CodeDom.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/netstandard2.0/System.CodeDom.dll": {}
        }
      },
      "System.Collections.Immutable/6.0.0": {
        "type": "package",
        "dependencies": {
          "System.Runtime.CompilerServices.Unsafe": "6.0.0"
        },
        "compile": {
          "lib/net6.0/System.Collections.Immutable.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Collections.Immutable.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.ComponentModel.Annotations/5.0.0": {
        "type": "package",
        "compile": {
          "ref/netstandard2.1/System.ComponentModel.Annotations.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/netstandard2.1/System.ComponentModel.Annotations.dll": {
            "related": ".xml"
          }
        }
      },
      "System.Composition/6.0.0": {
        "type": "package",
        "dependencies": {
          "System.Composition.AttributedModel": "6.0.0",
          "System.Composition.Convention": "6.0.0",
          "System.Composition.Hosting": "6.0.0",
          "System.Composition.Runtime": "6.0.0",
          "System.Composition.TypedParts": "6.0.0"
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Composition.AttributedModel/6.0.0": {
        "type": "package",
        "compile": {
          "lib/net6.0/System.Composition.AttributedModel.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Composition.AttributedModel.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Composition.Convention/6.0.0": {
        "type": "package",
        "dependencies": {
          "System.Composition.AttributedModel": "6.0.0"
        },
        "compile": {
          "lib/net6.0/System.Composition.Convention.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Composition.Convention.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Composition.Hosting/6.0.0": {
        "type": "package",
        "dependencies": {
          "System.Composition.Runtime": "6.0.0"
        },
        "compile": {
          "lib/net6.0/System.Composition.Hosting.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Composition.Hosting.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Composition.Runtime/6.0.0": {
        "type": "package",
        "compile": {
          "lib/net6.0/System.Composition.Runtime.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Composition.Runtime.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Composition.TypedParts/6.0.0": {
        "type": "package",
        "dependencies": {
          "System.Composition.AttributedModel": "6.0.0",
          "System.Composition.Hosting": "6.0.0",
          "System.Composition.Runtime": "6.0.0"
        },
        "compile": {
          "lib/net6.0/System.Composition.TypedParts.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Composition.TypedParts.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Drawing.Common/7.0.0": {
        "type": "package",
        "dependencies": {
          "Microsoft.Win32.SystemEvents": "7.0.0"
        },
        "compile": {
          "lib/net7.0/System.Drawing.Common.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/System.Drawing.Common.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        },
        "runtimeTargets": {
          "runtimes/win/lib/net7.0/System.Drawing.Common.dll": {
            "assetType": "runtime",
            "rid": "win"
          }
        }
      },
      "System.Formats.Asn1/7.0.0": {
        "type": "package",
        "compile": {
          "lib/net7.0/System.Formats.Asn1.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/System.Formats.Asn1.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "System.IO.Packaging/8.0.0": {
        "type": "package",
        "compile": {
          "lib/net8.0/System.IO.Packaging.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/System.IO.Packaging.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        }
      },
      "System.IO.Pipelines/6.0.3": {
        "type": "package",
        "compile": {
          "lib/net6.0/System.IO.Pipelines.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.IO.Pipelines.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Memory/4.5.3": {
        "type": "package",
        "compile": {
          "ref/netcoreapp2.1/_._": {}
        },
        "runtime": {
          "lib/netcoreapp2.1/_._": {}
        }
      },
      "System.Reflection.Metadata/6.0.1": {
        "type": "package",
        "dependencies": {
          "System.Collections.Immutable": "6.0.0"
        },
        "compile": {
          "lib/net6.0/System.Reflection.Metadata.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Reflection.Metadata.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Runtime.CompilerServices.Unsafe/6.0.0": {
        "type": "package",
        "compile": {
          "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      },
      "System.Security.Cryptography.Pkcs/7.0.2": {
        "type": "package",
        "dependencies": {
          "System.Formats.Asn1": "7.0.0"
        },
        "compile": {
          "lib/net7.0/System.Security.Cryptography.Pkcs.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/System.Security.Cryptography.Pkcs.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        },
        "runtimeTargets": {
          "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {
            "assetType": "runtime",
            "rid": "win"
          }
        }
      },
      "System.Text.Encoding.CodePages/7.0.0": {
        "type": "package",
        "compile": {
          "lib/net7.0/System.Text.Encoding.CodePages.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net7.0/System.Text.Encoding.CodePages.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        },
        "runtimeTargets": {
          "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll": {
            "assetType": "runtime",
            "rid": "win"
          }
        }
      },
      "System.Text.Encodings.Web/8.0.0": {
        "type": "package",
        "compile": {
          "lib/net8.0/System.Text.Encodings.Web.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/System.Text.Encodings.Web.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/_._": {}
        },
        "runtimeTargets": {
          "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {
            "assetType": "runtime",
            "rid": "browser"
          }
        }
      },
      "System.Text.Json/8.0.0": {
        "type": "package",
        "dependencies": {
          "System.Text.Encodings.Web": "8.0.0"
        },
        "compile": {
          "lib/net8.0/System.Text.Json.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net8.0/System.Text.Json.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/net6.0/System.Text.Json.targets": {}
        }
      },
      "System.Threading.Channels/6.0.0": {
        "type": "package",
        "compile": {
          "lib/net6.0/System.Threading.Channels.dll": {
            "related": ".xml"
          }
        },
        "runtime": {
          "lib/net6.0/System.Threading.Channels.dll": {
            "related": ".xml"
          }
        },
        "build": {
          "buildTransitive/netcoreapp3.1/_._": {}
        }
      }
    }
  },
  "libraries": {
    "DocumentFormat.OpenXml/3.0.2": {
      "sha512": "gqjP/BxKWgwAWwPAN+RpKaMOiidPM07F+mAxiiW184/MnkHnkk12Qo7cRB0wuq4kOZUrUzQ9EUO966hUy/wKBw==",
      "type": "package",
      "path": "documentformat.openxml/3.0.2",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "README.md",
        "documentformat.openxml.3.0.2.nupkg.sha512",
        "documentformat.openxml.nuspec",
        "icon.png",
        "lib/net35/DocumentFormat.OpenXml.dll",
        "lib/net35/DocumentFormat.OpenXml.xml",
        "lib/net40/DocumentFormat.OpenXml.dll",
        "lib/net40/DocumentFormat.OpenXml.xml",
        "lib/net46/DocumentFormat.OpenXml.dll",
        "lib/net46/DocumentFormat.OpenXml.xml",
        "lib/net8.0/DocumentFormat.OpenXml.dll",
        "lib/net8.0/DocumentFormat.OpenXml.xml",
        "lib/netstandard2.0/DocumentFormat.OpenXml.dll",
        "lib/netstandard2.0/DocumentFormat.OpenXml.xml"
      ]
    },
    "DocumentFormat.OpenXml.Framework/3.0.2": {
      "sha512": "ILRCJu2dUJDv27ZCQDftD7AZSuBgKg+ZSUF9dg2dk8qcIED7oM42y/cvZIm0mev80nj2QMzpxthX87+AebzHug==",
      "type": "package",
      "path": "documentformat.openxml.framework/3.0.2",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "README.md",
        "documentformat.openxml.framework.3.0.2.nupkg.sha512",
        "documentformat.openxml.framework.nuspec",
        "icon.png",
        "lib/net35/DocumentFormat.OpenXml.Framework.dll",
        "lib/net35/DocumentFormat.OpenXml.Framework.xml",
        "lib/net40/DocumentFormat.OpenXml.Framework.dll",
        "lib/net40/DocumentFormat.OpenXml.Framework.xml",
        "lib/net46/DocumentFormat.OpenXml.Framework.dll",
        "lib/net46/DocumentFormat.OpenXml.Framework.xml",
        "lib/net6.0/DocumentFormat.OpenXml.Framework.dll",
        "lib/net6.0/DocumentFormat.OpenXml.Framework.xml",
        "lib/net8.0/DocumentFormat.OpenXml.Framework.dll",
        "lib/net8.0/DocumentFormat.OpenXml.Framework.xml",
        "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.dll",
        "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.xml"
      ]
    },
    "EPPlus/7.0.0": {
      "sha512": "fqDPDAB0v+sU1m0VxDy4jBKhH77BoiaGjglnzflUVoHDL3Bnf8Xf/V8/dm3uYhMwChExnqFXxYscslu46seZQQ==",
      "type": "package",
      "path": "epplus/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "EPPlusLogo.png",
        "epplus.7.0.0.nupkg.sha512",
        "epplus.nuspec",
        "lib/net35/EPPlus.dll",
        "lib/net35/EPPlus.xml",
        "lib/net462/EPPlus.dll",
        "lib/net462/EPPlus.xml",
        "lib/net6.0/EPPlus.dll",
        "lib/net6.0/EPPlus.xml",
        "lib/net7.0/EPPlus.dll",
        "lib/net7.0/EPPlus.xml",
        "lib/netstandard2.0/EPPlus.dll",
        "lib/netstandard2.0/EPPlus.xml",
        "lib/netstandard2.1/EPPlus.dll",
        "lib/netstandard2.1/EPPlus.xml",
        "license.md",
        "readme.md",
        "readme.txt"
      ]
    },
    "EPPlus.Interfaces/6.1.1": {
      "sha512": "y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==",
      "type": "package",
      "path": "epplus.interfaces/6.1.1",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "EPPlusLogo.png",
        "epplus.interfaces.6.1.1.nupkg.sha512",
        "epplus.interfaces.nuspec",
        "lib/net35/EPPlus.Interfaces.dll",
        "lib/net462/EPPlus.Interfaces.dll",
        "lib/net6.0/EPPlus.Interfaces.dll",
        "lib/net7.0/EPPlus.Interfaces.dll",
        "lib/netstandard2.0/EPPlus.Interfaces.dll",
        "lib/netstandard2.1/EPPlus.Interfaces.dll",
        "license.md",
        "readme.md"
      ]
    },
    "EPPlus.System.Drawing/6.1.1": {
      "sha512": "lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==",
      "type": "package",
      "path": "epplus.system.drawing/6.1.1",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "EPPlusLogo.png",
        "epplus.system.drawing.6.1.1.nupkg.sha512",
        "epplus.system.drawing.nuspec",
        "lib/net35/EPPlus.System.Drawing.dll",
        "lib/net462/EPPlus.System.Drawing.dll",
        "lib/net6.0/EPPlus.System.Drawing.dll",
        "lib/net7.0/EPPlus.System.Drawing.dll",
        "lib/netstandard2.0/EPPlus.System.Drawing.dll",
        "lib/netstandard2.1/EPPlus.System.Drawing.dll",
        "license.md",
        "readme.md"
      ]
    },
    "Humanizer.Core/2.14.1": {
      "sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==",
      "type": "package",
      "path": "humanizer.core/2.14.1",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "humanizer.core.2.14.1.nupkg.sha512",
        "humanizer.core.nuspec",
        "lib/net6.0/Humanizer.dll",
        "lib/net6.0/Humanizer.xml",
        "lib/netstandard1.0/Humanizer.dll",
        "lib/netstandard1.0/Humanizer.xml",
        "lib/netstandard2.0/Humanizer.dll",
        "lib/netstandard2.0/Humanizer.xml",
        "logo.png"
      ]
    },
    "Microsoft.Bcl.AsyncInterfaces/6.0.0": {
      "sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==",
      "type": "package",
      "path": "microsoft.bcl.asyncinterfaces/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll",
        "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml",
        "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll",
        "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml",
        "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll",
        "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml",
        "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512",
        "microsoft.bcl.asyncinterfaces.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.CodeAnalysis.Analyzers/3.3.3": {
      "sha512": "j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==",
      "type": "package",
      "path": "microsoft.codeanalysis.analyzers/3.3.3",
      "hasTools": true,
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "ThirdPartyNotices.rtf",
        "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll",
        "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll",
        "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/zh-Hans/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll",
        "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll",
        "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/zh-Hans/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll",
        "build/Microsoft.CodeAnalysis.Analyzers.props",
        "build/Microsoft.CodeAnalysis.Analyzers.targets",
        "build/config/analysislevel_2_9_8_all.editorconfig",
        "build/config/analysislevel_2_9_8_default.editorconfig",
        "build/config/analysislevel_2_9_8_minimum.editorconfig",
        "build/config/analysislevel_2_9_8_none.editorconfig",
        "build/config/analysislevel_2_9_8_recommended.editorconfig",
        "build/config/analysislevel_3_3_all.editorconfig",
        "build/config/analysislevel_3_3_default.editorconfig",
        "build/config/analysislevel_3_3_minimum.editorconfig",
        "build/config/analysislevel_3_3_none.editorconfig",
        "build/config/analysislevel_3_3_recommended.editorconfig",
        "build/config/analysislevel_3_all.editorconfig",
        "build/config/analysislevel_3_default.editorconfig",
        "build/config/analysislevel_3_minimum.editorconfig",
        "build/config/analysislevel_3_none.editorconfig",
        "build/config/analysislevel_3_recommended.editorconfig",
        "build/config/analysislevelcorrectness_2_9_8_all.editorconfig",
        "build/config/analysislevelcorrectness_2_9_8_default.editorconfig",
        "build/config/analysislevelcorrectness_2_9_8_minimum.editorconfig",
        "build/config/analysislevelcorrectness_2_9_8_none.editorconfig",
        "build/config/analysislevelcorrectness_2_9_8_recommended.editorconfig",
        "build/config/analysislevelcorrectness_3_3_all.editorconfig",
        "build/config/analysislevelcorrectness_3_3_default.editorconfig",
        "build/config/analysislevelcorrectness_3_3_minimum.editorconfig",
        "build/config/analysislevelcorrectness_3_3_none.editorconfig",
        "build/config/analysislevelcorrectness_3_3_recommended.editorconfig",
        "build/config/analysislevelcorrectness_3_all.editorconfig",
        "build/config/analysislevelcorrectness_3_default.editorconfig",
        "build/config/analysislevelcorrectness_3_minimum.editorconfig",
        "build/config/analysislevelcorrectness_3_none.editorconfig",
        "build/config/analysislevelcorrectness_3_recommended.editorconfig",
        "build/config/analysislevellibrary_2_9_8_all.editorconfig",
        "build/config/analysislevellibrary_2_9_8_default.editorconfig",
        "build/config/analysislevellibrary_2_9_8_minimum.editorconfig",
        "build/config/analysislevellibrary_2_9_8_none.editorconfig",
        "build/config/analysislevellibrary_2_9_8_recommended.editorconfig",
        "build/config/analysislevellibrary_3_3_all.editorconfig",
        "build/config/analysislevellibrary_3_3_default.editorconfig",
        "build/config/analysislevellibrary_3_3_minimum.editorconfig",
        "build/config/analysislevellibrary_3_3_none.editorconfig",
        "build/config/analysislevellibrary_3_3_recommended.editorconfig",
        "build/config/analysislevellibrary_3_all.editorconfig",
        "build/config/analysislevellibrary_3_default.editorconfig",
        "build/config/analysislevellibrary_3_minimum.editorconfig",
        "build/config/analysislevellibrary_3_none.editorconfig",
        "build/config/analysislevellibrary_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.editorconfig",
        "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.editorconfig",
        "documentation/Analyzer Configuration.md",
        "documentation/Microsoft.CodeAnalysis.Analyzers.md",
        "documentation/Microsoft.CodeAnalysis.Analyzers.sarif",
        "editorconfig/AllRulesDefault/.editorconfig",
        "editorconfig/AllRulesDisabled/.editorconfig",
        "editorconfig/AllRulesEnabled/.editorconfig",
        "editorconfig/CorrectnessRulesDefault/.editorconfig",
        "editorconfig/CorrectnessRulesEnabled/.editorconfig",
        "editorconfig/DataflowRulesDefault/.editorconfig",
        "editorconfig/DataflowRulesEnabled/.editorconfig",
        "editorconfig/LibraryRulesDefault/.editorconfig",
        "editorconfig/LibraryRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig",
        "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig",
        "editorconfig/PortedFromFxCopRulesDefault/.editorconfig",
        "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig",
        "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512",
        "microsoft.codeanalysis.analyzers.nuspec",
        "rulesets/AllRulesDefault.ruleset",
        "rulesets/AllRulesDisabled.ruleset",
        "rulesets/AllRulesEnabled.ruleset",
        "rulesets/CorrectnessRulesDefault.ruleset",
        "rulesets/CorrectnessRulesEnabled.ruleset",
        "rulesets/DataflowRulesDefault.ruleset",
        "rulesets/DataflowRulesEnabled.ruleset",
        "rulesets/LibraryRulesDefault.ruleset",
        "rulesets/LibraryRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset",
        "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset",
        "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset",
        "rulesets/PortedFromFxCopRulesDefault.ruleset",
        "rulesets/PortedFromFxCopRulesEnabled.ruleset",
        "tools/install.ps1",
        "tools/uninstall.ps1"
      ]
    },
    "Microsoft.CodeAnalysis.Common/4.5.0": {
      "sha512": "lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==",
      "type": "package",
      "path": "microsoft.codeanalysis.common/4.5.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "ThirdPartyNotices.rtf",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml",
        "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll",
        "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.xml",
        "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll",
        "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll",
        "microsoft.codeanalysis.common.4.5.0.nupkg.sha512",
        "microsoft.codeanalysis.common.nuspec"
      ]
    },
    "Microsoft.CodeAnalysis.CSharp/4.5.0": {
      "sha512": "cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==",
      "type": "package",
      "path": "microsoft.codeanalysis.csharp/4.5.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "ThirdPartyNotices.rtf",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml",
        "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml",
        "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll",
        "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512",
        "microsoft.codeanalysis.csharp.nuspec"
      ]
    },
    "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {
      "sha512": "h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==",
      "type": "package",
      "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "ThirdPartyNotices.rtf",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.xml",
        "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml",
        "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll",
        "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512",
        "microsoft.codeanalysis.csharp.workspaces.nuspec"
      ]
    },
    "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {
      "sha512": "l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==",
      "type": "package",
      "path": "microsoft.codeanalysis.workspaces.common/4.5.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "ThirdPartyNotices.rtf",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.pdb",
        "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.xml",
        "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb",
        "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml",
        "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll",
        "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512",
        "microsoft.codeanalysis.workspaces.common.nuspec"
      ]
    },
    "Microsoft.Data.Sqlite.Core/8.0.0": {
      "sha512": "pujbzfszX7jAl7oTbHhqx7pxd9jibeyHHl8zy1gd55XMaKWjDtc5XhhNYwQnrwWYCInNdVoArbaaAvLgW7TwuA==",
      "type": "package",
      "path": "microsoft.data.sqlite.core/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "lib/net6.0/Microsoft.Data.Sqlite.dll",
        "lib/net6.0/Microsoft.Data.Sqlite.xml",
        "lib/net8.0/Microsoft.Data.Sqlite.dll",
        "lib/net8.0/Microsoft.Data.Sqlite.xml",
        "lib/netstandard2.0/Microsoft.Data.Sqlite.dll",
        "lib/netstandard2.0/Microsoft.Data.Sqlite.xml",
        "microsoft.data.sqlite.core.8.0.0.nupkg.sha512",
        "microsoft.data.sqlite.core.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore/8.0.0": {
      "sha512": "SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==",
      "type": "package",
      "path": "microsoft.entityframeworkcore/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props",
        "lib/net8.0/Microsoft.EntityFrameworkCore.dll",
        "lib/net8.0/Microsoft.EntityFrameworkCore.xml",
        "microsoft.entityframeworkcore.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {
      "sha512": "VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.abstractions/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml",
        "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.abstractions.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {
      "sha512": "ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.analyzers/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll",
        "lib/netstandard2.0/_._",
        "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.analyzers.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Design/8.0.0": {
      "sha512": "94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.design/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "build/net8.0/Microsoft.EntityFrameworkCore.Design.props",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Design.xml",
        "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.design.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Relational/8.0.0": {
      "sha512": "fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.relational/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml",
        "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.relational.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Sqlite/8.0.0": {
      "sha512": "hd3l+6Wyo4GwFAWa8J87L1X1ypYsk3za1lIsaF3U4X/tUJof/QPkuFbdfAADhmNqvqppmUL04RbgFM2nl5A7rQ==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.sqlite/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "lib/net8.0/_._",
        "microsoft.entityframeworkcore.sqlite.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.sqlite.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.0": {
      "sha512": "Vtnf4SIenAR0fp4OGEb83Dgn37lSMQqt6952e0f/6u/HNO4KQBKYiFw9vWIW4f4nNApre39WioW+jqaIVk15Wg==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.sqlite.core/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll",
        "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.xml",
        "microsoft.entityframeworkcore.sqlite.core.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.sqlite.core.nuspec"
      ]
    },
    "Microsoft.EntityFrameworkCore.Tools/8.0.0": {
      "sha512": "zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==",
      "type": "package",
      "path": "microsoft.entityframeworkcore.tools/8.0.0",
      "hasTools": true,
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "lib/net8.0/_._",
        "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512",
        "microsoft.entityframeworkcore.tools.nuspec",
        "tools/EntityFrameworkCore.PS2.psd1",
        "tools/EntityFrameworkCore.PS2.psm1",
        "tools/EntityFrameworkCore.psd1",
        "tools/EntityFrameworkCore.psm1",
        "tools/about_EntityFrameworkCore.help.txt",
        "tools/init.ps1",
        "tools/net461/any/ef.exe",
        "tools/net461/win-arm64/ef.exe",
        "tools/net461/win-x86/ef.exe",
        "tools/netcoreapp2.0/any/ef.dll",
        "tools/netcoreapp2.0/any/ef.runtimeconfig.json"
      ]
    },
    "Microsoft.Extensions.Caching.Abstractions/8.0.0": {
      "sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==",
      "type": "package",
      "path": "microsoft.extensions.caching.abstractions/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets",
        "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll",
        "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml",
        "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll",
        "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml",
        "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll",
        "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml",
        "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll",
        "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml",
        "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512",
        "microsoft.extensions.caching.abstractions.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Caching.Memory/8.0.0": {
      "sha512": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==",
      "type": "package",
      "path": "microsoft.extensions.caching.memory/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets",
        "lib/net462/Microsoft.Extensions.Caching.Memory.dll",
        "lib/net462/Microsoft.Extensions.Caching.Memory.xml",
        "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll",
        "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml",
        "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll",
        "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml",
        "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll",
        "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml",
        "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512",
        "microsoft.extensions.caching.memory.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Configuration/7.0.0": {
      "sha512": "tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==",
      "type": "package",
      "path": "microsoft.extensions.configuration/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Configuration.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets",
        "lib/net462/Microsoft.Extensions.Configuration.dll",
        "lib/net462/Microsoft.Extensions.Configuration.xml",
        "lib/net6.0/Microsoft.Extensions.Configuration.dll",
        "lib/net6.0/Microsoft.Extensions.Configuration.xml",
        "lib/net7.0/Microsoft.Extensions.Configuration.dll",
        "lib/net7.0/Microsoft.Extensions.Configuration.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml",
        "microsoft.extensions.configuration.7.0.0.nupkg.sha512",
        "microsoft.extensions.configuration.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {
      "sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==",
      "type": "package",
      "path": "microsoft.extensions.configuration.abstractions/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets",
        "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll",
        "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml",
        "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll",
        "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml",
        "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll",
        "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml",
        "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll",
        "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml",
        "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512",
        "microsoft.extensions.configuration.abstractions.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {
      "sha512": "xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==",
      "type": "package",
      "path": "microsoft.extensions.configuration.fileextensions/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets",
        "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll",
        "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml",
        "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll",
        "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml",
        "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll",
        "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml",
        "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512",
        "microsoft.extensions.configuration.fileextensions.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Configuration.Json/7.0.0": {
      "sha512": "LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==",
      "type": "package",
      "path": "microsoft.extensions.configuration.json/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets",
        "lib/net462/Microsoft.Extensions.Configuration.Json.dll",
        "lib/net462/Microsoft.Extensions.Configuration.Json.xml",
        "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll",
        "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml",
        "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll",
        "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml",
        "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll",
        "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml",
        "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512",
        "microsoft.extensions.configuration.json.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.DependencyInjection/8.0.0": {
      "sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==",
      "type": "package",
      "path": "microsoft.extensions.dependencyinjection/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets",
        "lib/net462/Microsoft.Extensions.DependencyInjection.dll",
        "lib/net462/Microsoft.Extensions.DependencyInjection.xml",
        "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll",
        "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml",
        "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll",
        "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml",
        "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll",
        "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml",
        "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll",
        "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml",
        "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll",
        "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml",
        "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512",
        "microsoft.extensions.dependencyinjection.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {
      "sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==",
      "type": "package",
      "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets",
        "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll",
        "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml",
        "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll",
        "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml",
        "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll",
        "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml",
        "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll",
        "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml",
        "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll",
        "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml",
        "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll",
        "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml",
        "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512",
        "microsoft.extensions.dependencyinjection.abstractions.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.DependencyModel/8.0.0": {
      "sha512": "NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==",
      "type": "package",
      "path": "microsoft.extensions.dependencymodel/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets",
        "lib/net462/Microsoft.Extensions.DependencyModel.dll",
        "lib/net462/Microsoft.Extensions.DependencyModel.xml",
        "lib/net6.0/Microsoft.Extensions.DependencyModel.dll",
        "lib/net6.0/Microsoft.Extensions.DependencyModel.xml",
        "lib/net7.0/Microsoft.Extensions.DependencyModel.dll",
        "lib/net7.0/Microsoft.Extensions.DependencyModel.xml",
        "lib/net8.0/Microsoft.Extensions.DependencyModel.dll",
        "lib/net8.0/Microsoft.Extensions.DependencyModel.xml",
        "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll",
        "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml",
        "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512",
        "microsoft.extensions.dependencymodel.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {
      "sha512": "NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==",
      "type": "package",
      "path": "microsoft.extensions.fileproviders.abstractions/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets",
        "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll",
        "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml",
        "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll",
        "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml",
        "lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.dll",
        "lib/net7.0/Microsoft.Extensions.FileProviders.Abstractions.xml",
        "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll",
        "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml",
        "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512",
        "microsoft.extensions.fileproviders.abstractions.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.FileProviders.Physical/7.0.0": {
      "sha512": "K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==",
      "type": "package",
      "path": "microsoft.extensions.fileproviders.physical/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets",
        "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll",
        "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml",
        "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll",
        "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml",
        "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll",
        "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml",
        "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll",
        "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml",
        "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512",
        "microsoft.extensions.fileproviders.physical.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {
      "sha512": "2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==",
      "type": "package",
      "path": "microsoft.extensions.filesystemglobbing/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets",
        "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll",
        "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml",
        "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll",
        "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml",
        "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll",
        "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml",
        "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll",
        "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml",
        "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512",
        "microsoft.extensions.filesystemglobbing.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Logging/8.0.0": {
      "sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==",
      "type": "package",
      "path": "microsoft.extensions.logging/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Logging.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets",
        "lib/net462/Microsoft.Extensions.Logging.dll",
        "lib/net462/Microsoft.Extensions.Logging.xml",
        "lib/net6.0/Microsoft.Extensions.Logging.dll",
        "lib/net6.0/Microsoft.Extensions.Logging.xml",
        "lib/net7.0/Microsoft.Extensions.Logging.dll",
        "lib/net7.0/Microsoft.Extensions.Logging.xml",
        "lib/net8.0/Microsoft.Extensions.Logging.dll",
        "lib/net8.0/Microsoft.Extensions.Logging.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Logging.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Logging.xml",
        "lib/netstandard2.1/Microsoft.Extensions.Logging.dll",
        "lib/netstandard2.1/Microsoft.Extensions.Logging.xml",
        "microsoft.extensions.logging.8.0.0.nupkg.sha512",
        "microsoft.extensions.logging.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Logging.Abstractions/8.0.0": {
      "sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==",
      "type": "package",
      "path": "microsoft.extensions.logging.abstractions/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll",
        "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll",
        "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll",
        "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/zh-Hans/Microsoft.Extensions.Logging.Generators.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll",
        "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets",
        "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets",
        "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets",
        "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets",
        "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll",
        "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml",
        "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll",
        "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml",
        "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll",
        "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml",
        "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll",
        "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml",
        "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512",
        "microsoft.extensions.logging.abstractions.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Options/8.0.0": {
      "sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==",
      "type": "package",
      "path": "microsoft.extensions.options/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll",
        "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/zh-Hans/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll",
        "buildTransitive/net461/Microsoft.Extensions.Options.targets",
        "buildTransitive/net462/Microsoft.Extensions.Options.targets",
        "buildTransitive/net6.0/Microsoft.Extensions.Options.targets",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets",
        "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets",
        "lib/net462/Microsoft.Extensions.Options.dll",
        "lib/net462/Microsoft.Extensions.Options.xml",
        "lib/net6.0/Microsoft.Extensions.Options.dll",
        "lib/net6.0/Microsoft.Extensions.Options.xml",
        "lib/net7.0/Microsoft.Extensions.Options.dll",
        "lib/net7.0/Microsoft.Extensions.Options.xml",
        "lib/net8.0/Microsoft.Extensions.Options.dll",
        "lib/net8.0/Microsoft.Extensions.Options.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Options.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Options.xml",
        "lib/netstandard2.1/Microsoft.Extensions.Options.dll",
        "lib/netstandard2.1/Microsoft.Extensions.Options.xml",
        "microsoft.extensions.options.8.0.0.nupkg.sha512",
        "microsoft.extensions.options.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.Extensions.Primitives/8.0.0": {
      "sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==",
      "type": "package",
      "path": "microsoft.extensions.primitives/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "PACKAGE.md",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Extensions.Primitives.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets",
        "lib/net462/Microsoft.Extensions.Primitives.dll",
        "lib/net462/Microsoft.Extensions.Primitives.xml",
        "lib/net6.0/Microsoft.Extensions.Primitives.dll",
        "lib/net6.0/Microsoft.Extensions.Primitives.xml",
        "lib/net7.0/Microsoft.Extensions.Primitives.dll",
        "lib/net7.0/Microsoft.Extensions.Primitives.xml",
        "lib/net8.0/Microsoft.Extensions.Primitives.dll",
        "lib/net8.0/Microsoft.Extensions.Primitives.xml",
        "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll",
        "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml",
        "microsoft.extensions.primitives.8.0.0.nupkg.sha512",
        "microsoft.extensions.primitives.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "Microsoft.IO.RecyclableMemoryStream/2.3.2": {
      "sha512": "Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==",
      "type": "package",
      "path": "microsoft.io.recyclablememorystream/2.3.2",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "README.md",
        "lib/net462/Microsoft.IO.RecyclableMemoryStream.dll",
        "lib/net462/Microsoft.IO.RecyclableMemoryStream.xml",
        "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll",
        "lib/net5.0/Microsoft.IO.RecyclableMemoryStream.xml",
        "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.dll",
        "lib/netcoreapp2.1/Microsoft.IO.RecyclableMemoryStream.xml",
        "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll",
        "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml",
        "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll",
        "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml",
        "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512",
        "microsoft.io.recyclablememorystream.nuspec"
      ]
    },
    "Microsoft.Win32.SystemEvents/7.0.0": {
      "sha512": "2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==",
      "type": "package",
      "path": "microsoft.win32.systemevents/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets",
        "lib/net462/Microsoft.Win32.SystemEvents.dll",
        "lib/net462/Microsoft.Win32.SystemEvents.xml",
        "lib/net6.0/Microsoft.Win32.SystemEvents.dll",
        "lib/net6.0/Microsoft.Win32.SystemEvents.xml",
        "lib/net7.0/Microsoft.Win32.SystemEvents.dll",
        "lib/net7.0/Microsoft.Win32.SystemEvents.xml",
        "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll",
        "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml",
        "microsoft.win32.systemevents.7.0.0.nupkg.sha512",
        "microsoft.win32.systemevents.nuspec",
        "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll",
        "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml",
        "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll",
        "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml",
        "useSharedDesignerContext.txt"
      ]
    },
    "Mono.TextTemplating/2.2.1": {
      "sha512": "KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==",
      "type": "package",
      "path": "mono.texttemplating/2.2.1",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "lib/net472/Mono.TextTemplating.dll",
        "lib/netstandard2.0/Mono.TextTemplating.dll",
        "mono.texttemplating.2.2.1.nupkg.sha512",
        "mono.texttemplating.nuspec"
      ]
    },
    "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {
      "sha512": "BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==",
      "type": "package",
      "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll",
        "lib/net461/SQLitePCLRaw.batteries_v2.dll",
        "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll",
        "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml",
        "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll",
        "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll",
        "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll",
        "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll",
        "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll",
        "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512",
        "sqlitepclraw.bundle_e_sqlite3.nuspec"
      ]
    },
    "SQLitePCLRaw.core/2.1.6": {
      "sha512": "wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==",
      "type": "package",
      "path": "sqlitepclraw.core/2.1.6",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "lib/netstandard2.0/SQLitePCLRaw.core.dll",
        "sqlitepclraw.core.2.1.6.nupkg.sha512",
        "sqlitepclraw.core.nuspec"
      ]
    },
    "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {
      "sha512": "2ObJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==",
      "type": "package",
      "path": "sqlitepclraw.lib.e_sqlite3/2.1.6",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets",
        "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets",
        "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets",
        "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets",
        "lib/net461/_._",
        "lib/netstandard2.0/_._",
        "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a",
        "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a",
        "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a",
        "runtimes/linux-arm/native/libe_sqlite3.so",
        "runtimes/linux-arm64/native/libe_sqlite3.so",
        "runtimes/linux-armel/native/libe_sqlite3.so",
        "runtimes/linux-mips64/native/libe_sqlite3.so",
        "runtimes/linux-musl-arm/native/libe_sqlite3.so",
        "runtimes/linux-musl-arm64/native/libe_sqlite3.so",
        "runtimes/linux-musl-x64/native/libe_sqlite3.so",
        "runtimes/linux-ppc64le/native/libe_sqlite3.so",
        "runtimes/linux-s390x/native/libe_sqlite3.so",
        "runtimes/linux-x64/native/libe_sqlite3.so",
        "runtimes/linux-x86/native/libe_sqlite3.so",
        "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib",
        "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib",
        "runtimes/osx-arm64/native/libe_sqlite3.dylib",
        "runtimes/osx-x64/native/libe_sqlite3.dylib",
        "runtimes/win-arm/native/e_sqlite3.dll",
        "runtimes/win-arm64/native/e_sqlite3.dll",
        "runtimes/win-x64/native/e_sqlite3.dll",
        "runtimes/win-x86/native/e_sqlite3.dll",
        "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll",
        "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll",
        "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll",
        "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll",
        "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512",
        "sqlitepclraw.lib.e_sqlite3.nuspec"
      ]
    },
    "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {
      "sha512": "PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==",
      "type": "package",
      "path": "sqlitepclraw.provider.e_sqlite3/2.1.6",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll",
        "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll",
        "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll",
        "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512",
        "sqlitepclraw.provider.e_sqlite3.nuspec"
      ]
    },
    "System.CodeDom/4.4.0": {
      "sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==",
      "type": "package",
      "path": "system.codedom/4.4.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "lib/net461/System.CodeDom.dll",
        "lib/netstandard2.0/System.CodeDom.dll",
        "ref/net461/System.CodeDom.dll",
        "ref/net461/System.CodeDom.xml",
        "ref/netstandard2.0/System.CodeDom.dll",
        "ref/netstandard2.0/System.CodeDom.xml",
        "system.codedom.4.4.0.nupkg.sha512",
        "system.codedom.nuspec",
        "useSharedDesignerContext.txt",
        "version.txt"
      ]
    },
    "System.Collections.Immutable/6.0.0": {
      "sha512": "l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==",
      "type": "package",
      "path": "system.collections.immutable/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Collections.Immutable.dll",
        "lib/net461/System.Collections.Immutable.xml",
        "lib/net6.0/System.Collections.Immutable.dll",
        "lib/net6.0/System.Collections.Immutable.xml",
        "lib/netstandard2.0/System.Collections.Immutable.dll",
        "lib/netstandard2.0/System.Collections.Immutable.xml",
        "system.collections.immutable.6.0.0.nupkg.sha512",
        "system.collections.immutable.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.ComponentModel.Annotations/5.0.0": {
      "sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==",
      "type": "package",
      "path": "system.componentmodel.annotations/5.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "lib/MonoAndroid10/_._",
        "lib/MonoTouch10/_._",
        "lib/net45/_._",
        "lib/net461/System.ComponentModel.Annotations.dll",
        "lib/netcore50/System.ComponentModel.Annotations.dll",
        "lib/netstandard1.4/System.ComponentModel.Annotations.dll",
        "lib/netstandard2.0/System.ComponentModel.Annotations.dll",
        "lib/netstandard2.1/System.ComponentModel.Annotations.dll",
        "lib/netstandard2.1/System.ComponentModel.Annotations.xml",
        "lib/portable-net45+win8/_._",
        "lib/win8/_._",
        "lib/xamarinios10/_._",
        "lib/xamarinmac20/_._",
        "lib/xamarintvos10/_._",
        "lib/xamarinwatchos10/_._",
        "ref/MonoAndroid10/_._",
        "ref/MonoTouch10/_._",
        "ref/net45/_._",
        "ref/net461/System.ComponentModel.Annotations.dll",
        "ref/net461/System.ComponentModel.Annotations.xml",
        "ref/netcore50/System.ComponentModel.Annotations.dll",
        "ref/netcore50/System.ComponentModel.Annotations.xml",
        "ref/netcore50/de/System.ComponentModel.Annotations.xml",
        "ref/netcore50/es/System.ComponentModel.Annotations.xml",
        "ref/netcore50/fr/System.ComponentModel.Annotations.xml",
        "ref/netcore50/it/System.ComponentModel.Annotations.xml",
        "ref/netcore50/ja/System.ComponentModel.Annotations.xml",
        "ref/netcore50/ko/System.ComponentModel.Annotations.xml",
        "ref/netcore50/ru/System.ComponentModel.Annotations.xml",
        "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml",
        "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/System.ComponentModel.Annotations.dll",
        "ref/netstandard1.1/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/System.ComponentModel.Annotations.dll",
        "ref/netstandard1.3/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/System.ComponentModel.Annotations.dll",
        "ref/netstandard1.4/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml",
        "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml",
        "ref/netstandard2.0/System.ComponentModel.Annotations.dll",
        "ref/netstandard2.0/System.ComponentModel.Annotations.xml",
        "ref/netstandard2.1/System.ComponentModel.Annotations.dll",
        "ref/netstandard2.1/System.ComponentModel.Annotations.xml",
        "ref/portable-net45+win8/_._",
        "ref/win8/_._",
        "ref/xamarinios10/_._",
        "ref/xamarinmac20/_._",
        "ref/xamarintvos10/_._",
        "ref/xamarinwatchos10/_._",
        "system.componentmodel.annotations.5.0.0.nupkg.sha512",
        "system.componentmodel.annotations.nuspec",
        "useSharedDesignerContext.txt",
        "version.txt"
      ]
    },
    "System.Composition/6.0.0": {
      "sha512": "d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==",
      "type": "package",
      "path": "system.composition/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Composition.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "system.composition.6.0.0.nupkg.sha512",
        "system.composition.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Composition.AttributedModel/6.0.0": {
      "sha512": "WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==",
      "type": "package",
      "path": "system.composition.attributedmodel/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Composition.AttributedModel.dll",
        "lib/net461/System.Composition.AttributedModel.xml",
        "lib/net6.0/System.Composition.AttributedModel.dll",
        "lib/net6.0/System.Composition.AttributedModel.xml",
        "lib/netstandard2.0/System.Composition.AttributedModel.dll",
        "lib/netstandard2.0/System.Composition.AttributedModel.xml",
        "system.composition.attributedmodel.6.0.0.nupkg.sha512",
        "system.composition.attributedmodel.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Composition.Convention/6.0.0": {
      "sha512": "XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==",
      "type": "package",
      "path": "system.composition.convention/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Composition.Convention.dll",
        "lib/net461/System.Composition.Convention.xml",
        "lib/net6.0/System.Composition.Convention.dll",
        "lib/net6.0/System.Composition.Convention.xml",
        "lib/netstandard2.0/System.Composition.Convention.dll",
        "lib/netstandard2.0/System.Composition.Convention.xml",
        "system.composition.convention.6.0.0.nupkg.sha512",
        "system.composition.convention.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Composition.Hosting/6.0.0": {
      "sha512": "w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==",
      "type": "package",
      "path": "system.composition.hosting/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Composition.Hosting.dll",
        "lib/net461/System.Composition.Hosting.xml",
        "lib/net6.0/System.Composition.Hosting.dll",
        "lib/net6.0/System.Composition.Hosting.xml",
        "lib/netstandard2.0/System.Composition.Hosting.dll",
        "lib/netstandard2.0/System.Composition.Hosting.xml",
        "system.composition.hosting.6.0.0.nupkg.sha512",
        "system.composition.hosting.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Composition.Runtime/6.0.0": {
      "sha512": "qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==",
      "type": "package",
      "path": "system.composition.runtime/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Composition.Runtime.dll",
        "lib/net461/System.Composition.Runtime.xml",
        "lib/net6.0/System.Composition.Runtime.dll",
        "lib/net6.0/System.Composition.Runtime.xml",
        "lib/netstandard2.0/System.Composition.Runtime.dll",
        "lib/netstandard2.0/System.Composition.Runtime.xml",
        "system.composition.runtime.6.0.0.nupkg.sha512",
        "system.composition.runtime.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Composition.TypedParts/6.0.0": {
      "sha512": "iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==",
      "type": "package",
      "path": "system.composition.typedparts/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Composition.TypedParts.dll",
        "lib/net461/System.Composition.TypedParts.xml",
        "lib/net6.0/System.Composition.TypedParts.dll",
        "lib/net6.0/System.Composition.TypedParts.xml",
        "lib/netstandard2.0/System.Composition.TypedParts.dll",
        "lib/netstandard2.0/System.Composition.TypedParts.xml",
        "system.composition.typedparts.6.0.0.nupkg.sha512",
        "system.composition.typedparts.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Drawing.Common/7.0.0": {
      "sha512": "KIX+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==",
      "type": "package",
      "path": "system.drawing.common/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/System.Drawing.Common.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets",
        "lib/MonoAndroid10/_._",
        "lib/MonoTouch10/_._",
        "lib/net462/System.Drawing.Common.dll",
        "lib/net462/System.Drawing.Common.xml",
        "lib/net6.0/System.Drawing.Common.dll",
        "lib/net6.0/System.Drawing.Common.xml",
        "lib/net7.0/System.Drawing.Common.dll",
        "lib/net7.0/System.Drawing.Common.xml",
        "lib/netstandard2.0/System.Drawing.Common.dll",
        "lib/netstandard2.0/System.Drawing.Common.xml",
        "lib/xamarinios10/_._",
        "lib/xamarinmac20/_._",
        "lib/xamarintvos10/_._",
        "lib/xamarinwatchos10/_._",
        "runtimes/win/lib/net6.0/System.Drawing.Common.dll",
        "runtimes/win/lib/net6.0/System.Drawing.Common.xml",
        "runtimes/win/lib/net7.0/System.Drawing.Common.dll",
        "runtimes/win/lib/net7.0/System.Drawing.Common.xml",
        "system.drawing.common.7.0.0.nupkg.sha512",
        "system.drawing.common.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Formats.Asn1/7.0.0": {
      "sha512": "+nfpV0afLmvJW8+pLlHxRjz3oZJw4fkyU9MMEaMhCsHi/SN9bGF9q79ROubDiwTiCHezmK0uCWkPP7tGFP/4yg==",
      "type": "package",
      "path": "system.formats.asn1/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/System.Formats.Asn1.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets",
        "lib/net462/System.Formats.Asn1.dll",
        "lib/net462/System.Formats.Asn1.xml",
        "lib/net6.0/System.Formats.Asn1.dll",
        "lib/net6.0/System.Formats.Asn1.xml",
        "lib/net7.0/System.Formats.Asn1.dll",
        "lib/net7.0/System.Formats.Asn1.xml",
        "lib/netstandard2.0/System.Formats.Asn1.dll",
        "lib/netstandard2.0/System.Formats.Asn1.xml",
        "system.formats.asn1.7.0.0.nupkg.sha512",
        "system.formats.asn1.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.IO.Packaging/8.0.0": {
      "sha512": "8g1V4YRpdGAxFcK8v9OjuMdIOJSpF30Zb1JGicwVZhly3I994WFyBdV6mQEo8d3T+URQe55/M0U0eIH0Hts1bg==",
      "type": "package",
      "path": "system.io.packaging/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/System.IO.Packaging.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets",
        "lib/net462/System.IO.Packaging.dll",
        "lib/net462/System.IO.Packaging.xml",
        "lib/net6.0/System.IO.Packaging.dll",
        "lib/net6.0/System.IO.Packaging.xml",
        "lib/net7.0/System.IO.Packaging.dll",
        "lib/net7.0/System.IO.Packaging.xml",
        "lib/net8.0/System.IO.Packaging.dll",
        "lib/net8.0/System.IO.Packaging.xml",
        "lib/netstandard2.0/System.IO.Packaging.dll",
        "lib/netstandard2.0/System.IO.Packaging.xml",
        "system.io.packaging.8.0.0.nupkg.sha512",
        "system.io.packaging.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.IO.Pipelines/6.0.3": {
      "sha512": "ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==",
      "type": "package",
      "path": "system.io.pipelines/6.0.3",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.IO.Pipelines.dll",
        "lib/net461/System.IO.Pipelines.xml",
        "lib/net6.0/System.IO.Pipelines.dll",
        "lib/net6.0/System.IO.Pipelines.xml",
        "lib/netcoreapp3.1/System.IO.Pipelines.dll",
        "lib/netcoreapp3.1/System.IO.Pipelines.xml",
        "lib/netstandard2.0/System.IO.Pipelines.dll",
        "lib/netstandard2.0/System.IO.Pipelines.xml",
        "system.io.pipelines.6.0.3.nupkg.sha512",
        "system.io.pipelines.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Memory/4.5.3": {
      "sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==",
      "type": "package",
      "path": "system.memory/4.5.3",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "lib/netcoreapp2.1/_._",
        "lib/netstandard1.1/System.Memory.dll",
        "lib/netstandard1.1/System.Memory.xml",
        "lib/netstandard2.0/System.Memory.dll",
        "lib/netstandard2.0/System.Memory.xml",
        "ref/netcoreapp2.1/_._",
        "system.memory.4.5.3.nupkg.sha512",
        "system.memory.nuspec",
        "useSharedDesignerContext.txt",
        "version.txt"
      ]
    },
    "System.Reflection.Metadata/6.0.1": {
      "sha512": "III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==",
      "type": "package",
      "path": "system.reflection.metadata/6.0.1",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Reflection.Metadata.dll",
        "lib/net461/System.Reflection.Metadata.xml",
        "lib/net6.0/System.Reflection.Metadata.dll",
        "lib/net6.0/System.Reflection.Metadata.xml",
        "lib/netstandard2.0/System.Reflection.Metadata.dll",
        "lib/netstandard2.0/System.Reflection.Metadata.xml",
        "system.reflection.metadata.6.0.1.nupkg.sha512",
        "system.reflection.metadata.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Runtime.CompilerServices.Unsafe/6.0.0": {
      "sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==",
      "type": "package",
      "path": "system.runtime.compilerservices.unsafe/6.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets",
        "buildTransitive/netcoreapp3.1/_._",
        "lib/net461/System.Runtime.CompilerServices.Unsafe.dll",
        "lib/net461/System.Runtime.CompilerServices.Unsafe.xml",
        "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll",
        "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml",
        "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll",
        "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml",
        "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll",
        "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml",
        "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512",
        "system.runtime.compilerservices.unsafe.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Security.Cryptography.Pkcs/7.0.2": {
      "sha512": "xhFNJOcQSWhpiVGLLBQYoxAltQSQVycMkwaX1z7I7oEdT9Wr0HzSM1yeAbfoHaERIYd5s6EpLSOLs2qMchSKlA==",
      "type": "package",
      "path": "system.security.cryptography.pkcs/7.0.2",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets",
        "lib/net462/System.Security.Cryptography.Pkcs.dll",
        "lib/net462/System.Security.Cryptography.Pkcs.xml",
        "lib/net6.0/System.Security.Cryptography.Pkcs.dll",
        "lib/net6.0/System.Security.Cryptography.Pkcs.xml",
        "lib/net7.0/System.Security.Cryptography.Pkcs.dll",
        "lib/net7.0/System.Security.Cryptography.Pkcs.xml",
        "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll",
        "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml",
        "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll",
        "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml",
        "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll",
        "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml",
        "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll",
        "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml",
        "system.security.cryptography.pkcs.7.0.2.nupkg.sha512",
        "system.security.cryptography.pkcs.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Text.Encoding.CodePages/7.0.0": {
      "sha512": "LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==",
      "type": "package",
      "path": "system.text.encoding.codepages/7.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/System.Text.Encoding.CodePages.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets",
        "lib/MonoAndroid10/_._",
        "lib/MonoTouch10/_._",
        "lib/net462/System.Text.Encoding.CodePages.dll",
        "lib/net462/System.Text.Encoding.CodePages.xml",
        "lib/net6.0/System.Text.Encoding.CodePages.dll",
        "lib/net6.0/System.Text.Encoding.CodePages.xml",
        "lib/net7.0/System.Text.Encoding.CodePages.dll",
        "lib/net7.0/System.Text.Encoding.CodePages.xml",
        "lib/netstandard2.0/System.Text.Encoding.CodePages.dll",
        "lib/netstandard2.0/System.Text.Encoding.CodePages.xml",
        "lib/xamarinios10/_._",
        "lib/xamarinmac20/_._",
        "lib/xamarintvos10/_._",
        "lib/xamarinwatchos10/_._",
        "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll",
        "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml",
        "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll",
        "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.xml",
        "system.text.encoding.codepages.7.0.0.nupkg.sha512",
        "system.text.encoding.codepages.nuspec",
        "useSharedDesignerContext.txt"
      ]
    },
    "System.Text.Encodings.Web/8.0.0": {
      "sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==",
      "type": "package",
      "path": "system.text.encodings.web/8.0.0",
      "files": [
        ".nupkg.metadata",
        ".signature.p7s",
        "Icon.png",
        "LICENSE.TXT",
        "THIRD-PARTY-NOTICES.TXT",
        "buildTransitive/net461/System.Text.Encodings.Web.targets",
        "buildTransitive/net462/_._",
        "buildTransitive/net6.0/_._",
        "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets",
        "lib/net462/System.Text.Encodings.Web.dll",
        "lib/net462/System.Text.Encodings.Web.xml",
        "lib/net6.0/System.Text.Encodings.Web.dll",
        "lib/net6.0/System.Text.Encodings.Web.xml",
        "lib/net7.0/System.Text.Encodings.Web.dll",
        "lib/net7.0/System.Text.Encodings.Web.xml",
        "lib/net8.0/System.Text.Encodings.Web.dll",
        "lib/net8.0/System.Text.Encodings.Web.xml",
        "lib/netstandard2.0/System.Text.Encodings.Web.dll",
        "lib/netstandard2.0/System.Text.Encodings.Web.xml",
        "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll",
        "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml",
        "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll",
        "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml",
        "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll",
        "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml",
        "system.text.encodings.web.8.0.0.nupkg.sha512",
        "system.te