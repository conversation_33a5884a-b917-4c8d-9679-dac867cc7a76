using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Models.Reports;
using TYFManagementSystem.Services.Reports;

namespace TYFManagementSystem.Views.Reports
{
    /// <summary>
    /// Interaction logic for ReportPreviewDialog.xaml
    /// </summary>
    public partial class ReportPreviewDialog : Window
    {
        private readonly ReportGenerationResult _reportResult;
        private readonly ReportService _reportService;

        public ReportPreviewDialog(ReportGenerationResult reportResult)
        {
            InitializeComponent();
            _reportResult = reportResult;
            _reportService = new ReportService();
            
            LoadReportData();
        }

        private void LoadReportData()
        {
            try
            {
                // تحديث معلومات التقرير
                if (_reportResult.Report != null)
                {
                    ReportTitleTextBlock.Text = $"معاينة التقرير: {_reportResult.Report.Name}";
                    ReportInfoTextBlock.Text = $"{_reportResult.Report.Description} | {_reportResult.RecordCount} سجل";
                }

                // تحديث الإحصائيات السريعة
                RecordCountTextBlock.Text = _reportResult.RecordCount.ToString();
                GenerationTimeTextBlock.Text = $"{_reportResult.GenerationTime.TotalSeconds:F1} ثانية";
                ReportTypeTextBlock.Text = GetReportTypeText(_reportResult.Report?.Type ?? ReportType.IPTT);
                GenerationDateTextBlock.Text = $"• تاريخ الإنشاء: {DateTime.Now:dd/MM/yyyy HH:mm}";

                // تحميل البيانات في الجدول
                LoadDataGrid();

                // تحديث الملخص
                UpdateSummary();

                StatusTextBlock.Text = $"تم تحميل {_reportResult.RecordCount} سجل بنجاح";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDataGrid()
        {
            try
            {
                if (_reportResult.Data == null || !_reportResult.Data.Any())
                {
                    StatusTextBlock.Text = "لا توجد بيانات للعرض";
                    return;
                }

                // إنشاء DataTable من البيانات
                var dataTable = new DataTable();
                
                // إضافة الأعمدة
                var firstRow = _reportResult.Data.First();
                foreach (var column in firstRow.Values.Keys)
                {
                    dataTable.Columns.Add(column, typeof(string));
                }

                // إضافة الصفوف
                foreach (var row in _reportResult.Data)
                {
                    var dataRow = dataTable.NewRow();
                    foreach (var kvp in row.Values)
                    {
                        dataRow[kvp.Key] = kvp.Value?.ToString() ?? "";
                    }
                    dataTable.Rows.Add(dataRow);
                }

                ReportDataGrid.ItemsSource = dataTable.DefaultView;
            }
            catch (Exception ex)
            {
                StatusTextBlock.Text = $"خطأ في تحميل الجدول: {ex.Message}";
            }
        }

        private void UpdateSummary()
        {
            try
            {
                var data = _reportResult.Data;
                if (data == null || !data.Any())
                    return;

                // حساب الإحصائيات
                var projectCount = data.Select(r => r.Values.ContainsKey("المشروع") ? r.Values["المشروع"] : "").Distinct().Count();
                var indicatorCount = data.Count;
                var dataTypeCount = data.Sum(r => 
                {
                    if (r.Values.ContainsKey("أنواع البيانات") && 
                        int.TryParse(r.Values["أنواع البيانات"]?.ToString(), out int count))
                        return count;
                    return 0;
                });

                // حساب متوسط الإنجاز
                var achievements = data.Where(r => r.Values.ContainsKey("نسبة الإنجاز"))
                    .Select(r => r.Values["نسبة الإنجاز"]?.ToString())
                    .Where(s => !string.IsNullOrEmpty(s))
                    .Select(s => 
                    {
                        var cleanValue = s.Replace("%", "");
                        return double.TryParse(cleanValue, out double val) ? val : 0;
                    })
                    .Where(v => v > 0);

                var avgAchievement = achievements.Any() ? achievements.Average() : 0;

                // تحديث النصوص
                SummaryText1.Text = $"• إجمالي المؤشرات: {indicatorCount}";
                SummaryText2.Text = $"• إجمالي المشاريع: {projectCount}";
                SummaryText3.Text = $"• إجمالي أنواع البيانات: {dataTypeCount}";
                SummaryText4.Text = $"• متوسط الإنجاز: {avgAchievement:F1}%";

                // تفاصيل إضافية
                var details = $"تم إنشاء هذا التقرير في {_reportResult.GenerationTime.TotalSeconds:F1} ثانية.\n";
                details += $"يحتوي التقرير على {indicatorCount} مؤشر من {projectCount} مشروع.\n";
                details += $"إجمالي أنواع البيانات المتضمنة: {dataTypeCount}";
                
                AdditionalDetailsTextBlock.Text = details;
            }
            catch (Exception ex)
            {
                AdditionalDetailsTextBlock.Text = $"خطأ في حساب الملخص: {ex.Message}";
            }
        }

        private string GetReportTypeText(ReportType type)
        {
            return type switch
            {
                ReportType.IPTT => "IPTT",
                ReportType.Financial => "مالي",
                ReportType.Activities => "أنشطة",
                ReportType.Projects => "مشاريع",
                ReportType.Custom => "مخصص",
                ReportType.Summary => "ملخص",
                ReportType.Detailed => "مفصل",
                _ => "غير محدد"
            };
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var exportDialog = new ExportReportDialog(_reportResult);
                exportDialog.Owner = this;
                var result = exportDialog.ShowDialog();

                if (result == true)
                {
                    StatusTextBlock.Text = "تم تصدير التقرير بنجاح";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("ميزة الطباعة ستكون متاحة قريباً", "قريباً", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_reportResult.Report == null)
                {
                    MessageBox.Show("لا يوجد تقرير للحفظ", "تحذير", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                SaveButton.IsEnabled = false;
                var originalContent = ((Button)sender).Content;
                ((Button)sender).Content = "⏳ جاري الحفظ...";

                var saved = await _reportService.SaveReportAsync(_reportResult.Report);
                
                if (saved)
                {
                    MessageBox.Show("تم حفظ التقرير بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    StatusTextBlock.Text = "تم حفظ التقرير";
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التقرير", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                SaveButton.IsEnabled = true;
                ((Button)sender).Content = originalContent;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                SaveButton.IsEnabled = true;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _reportService?.Dispose();
            base.OnClosed(e);
        }
    }
}
