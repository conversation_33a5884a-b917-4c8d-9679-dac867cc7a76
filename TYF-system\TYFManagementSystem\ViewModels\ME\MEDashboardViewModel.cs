using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.ME;
using TYFManagementSystem.Services.ME;
using TYFManagementSystem.Services;
using TYFManagementSystem.Views.IPTT;

namespace TYFManagementSystem.ViewModels.ME
{
    /// <summary>
    /// ViewModel للوحة تحكم المتابعة والتقييم
    /// </summary>
    public class MEDashboardViewModel : BaseViewModel, IDisposable
    {
        #region Private Fields
        private readonly MEService _meService;
        private MEDashboardData _dashboardData;
        private ObservableCollection<ProjectSummary> _projectSummaries;
        private ObservableCollection<IndicatorProgress> _indicatorProgress;
        private ObservableCollection<MEAlert> _alerts;
        private OverallStatistics _overallStats;
        private ProjectSummary? _selectedProject;
        private IndicatorProgress? _selectedIndicator;
        private MEAlert? _selectedAlert;
        private bool _isLoading;
        private string _statusMessage = "";
        private DateTime _lastUpdated;
        private string _selectedTimeFilter = "الكل";
        private string _selectedStatusFilter = "الكل";
        #endregion

        #region Properties
        public MEDashboardData DashboardData
        {
            get => _dashboardData;
            set => SetProperty(ref _dashboardData, value);
        }

        public ObservableCollection<ProjectSummary> ProjectSummaries
        {
            get => _projectSummaries;
            set => SetProperty(ref _projectSummaries, value);
        }

        public ObservableCollection<IndicatorProgress> IndicatorProgress
        {
            get => _indicatorProgress;
            set => SetProperty(ref _indicatorProgress, value);
        }

        public ObservableCollection<MEAlert> Alerts
        {
            get => _alerts;
            set => SetProperty(ref _alerts, value);
        }

        public OverallStatistics OverallStats
        {
            get => _overallStats;
            set => SetProperty(ref _overallStats, value);
        }

        public ProjectSummary? SelectedProject
        {
            get => _selectedProject;
            set => SetProperty(ref _selectedProject, value);
        }

        public IndicatorProgress? SelectedIndicator
        {
            get => _selectedIndicator;
            set => SetProperty(ref _selectedIndicator, value);
        }

        public MEAlert? SelectedAlert
        {
            get => _selectedAlert;
            set => SetProperty(ref _selectedAlert, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public DateTime LastUpdated
        {
            get => _lastUpdated;
            set => SetProperty(ref _lastUpdated, value);
        }

        public string SelectedTimeFilter
        {
            get => _selectedTimeFilter;
            set
            {
                if (SetProperty(ref _selectedTimeFilter, value))
                {
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public string SelectedStatusFilter
        {
            get => _selectedStatusFilter;
            set
            {
                if (SetProperty(ref _selectedStatusFilter, value))
                {
                    _ = ApplyFiltersAsync();
                }
            }
        }

        // خيارات الفلاتر
        public string[] TimeFilterOptions { get; } = { "الكل", "هذا الشهر", "هذا الربع", "هذا العام" };
        public string[] StatusFilterOptions { get; } = { "الكل", "على المسار", "متأخر", "في خطر" };
        #endregion

        #region Commands
        public ICommand LoadDashboardCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand ViewProjectDetailsCommand { get; private set; }
        public ICommand ViewIndicatorDetailsCommand { get; private set; }
        public ICommand MarkAlertAsReadCommand { get; private set; }
        public ICommand GenerateReportCommand { get; private set; }
        public ICommand ExportDataCommand { get; private set; }
        public ICommand OpenIpttCommand { get; private set; }
        #endregion

        #region Constructor
        public MEDashboardViewModel()
        {
            _meService = new MEService();
            _dashboardData = new MEDashboardData();
            _projectSummaries = new ObservableCollection<ProjectSummary>();
            _indicatorProgress = new ObservableCollection<IndicatorProgress>();
            _alerts = new ObservableCollection<MEAlert>();
            _overallStats = new OverallStatistics();

            InitializeCommands();
            _ = LoadDashboardAsync();
        }
        #endregion

        #region Private Methods
        private void InitializeCommands()
        {
            LoadDashboardCommand = new RelayCommand(async () => await LoadDashboardAsync());
            RefreshCommand = new RelayCommand(async () => await RefreshAsync());
            ViewProjectDetailsCommand = new RelayCommand(async () => await ViewProjectDetailsAsync(), () => SelectedProject != null);
            ViewIndicatorDetailsCommand = new RelayCommand(async () => await ViewIndicatorDetailsAsync(), () => SelectedIndicator != null);
            MarkAlertAsReadCommand = new RelayCommand(async () => await MarkAlertAsReadAsync(), () => SelectedAlert != null);
            GenerateReportCommand = new RelayCommand(async () => await GenerateReportAsync());
            ExportDataCommand = new RelayCommand(async () => await ExportDataAsync());
            OpenIpttCommand = new RelayCommand(() => OpenIpttWindow(), () => SelectedProject != null);
        }

        private async Task LoadDashboardAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل بيانات لوحة التحكم...";

                DashboardData = await _meService.GetDashboardDataAsync();

                // تحديث المجموعات
                ProjectSummaries.Clear();
                foreach (var project in DashboardData.ProjectSummaries)
                {
                    ProjectSummaries.Add(project);
                }

                IndicatorProgress.Clear();
                foreach (var indicator in DashboardData.IndicatorProgress)
                {
                    IndicatorProgress.Add(indicator);
                }

                Alerts.Clear();
                foreach (var alert in DashboardData.Alerts)
                {
                    Alerts.Add(alert);
                }

                OverallStats = DashboardData.OverallStats;
                LastUpdated = DashboardData.LastUpdated;

                StatusMessage = $"تم تحميل {ProjectSummaries.Count} مشروع و {IndicatorProgress.Count} مؤشر";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ApplyFiltersAsync()
        {
            try
            {
                // تطبيق فلاتر الوقت والحالة
                var filteredProjects = DashboardData.ProjectSummaries.AsEnumerable();
                var filteredIndicators = DashboardData.IndicatorProgress.AsEnumerable();

                // فلتر الحالة
                if (SelectedStatusFilter != "الكل")
                {
                    filteredIndicators = SelectedStatusFilter switch
                    {
                        "على المسار" => filteredIndicators.Where(i => i.Status == IndicatorStatus.OnTrack),
                        "متأخر" => filteredIndicators.Where(i => i.Status == IndicatorStatus.BehindSchedule),
                        "في خطر" => filteredIndicators.Where(i => i.Status == IndicatorStatus.AtRisk),
                        _ => filteredIndicators
                    };
                }

                // فلتر الوقت
                if (SelectedTimeFilter != "الكل")
                {
                    var filterDate = SelectedTimeFilter switch
                    {
                        "هذا الشهر" => DateTime.Now.AddMonths(-1),
                        "هذا الربع" => DateTime.Now.AddMonths(-3),
                        "هذا العام" => DateTime.Now.AddYears(-1),
                        _ => DateTime.MinValue
                    };

                    if (filterDate != DateTime.MinValue)
                    {
                        filteredIndicators = filteredIndicators.Where(i => i.LastUpdated >= filterDate);
                    }
                }

                // تحديث المجموعات المفلترة
                IndicatorProgress.Clear();
                foreach (var indicator in filteredIndicators)
                {
                    IndicatorProgress.Add(indicator);
                }

                StatusMessage = $"تم تطبيق الفلاتر - {IndicatorProgress.Count} مؤشر";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تطبيق الفلاتر: {ex.Message}";
            }
        }

        private async Task RefreshAsync()
        {
            await LoadDashboardAsync();
        }

        private async Task ViewProjectDetailsAsync()
        {
            if (SelectedProject == null) return;

            try
            {
                StatusMessage = $"عرض تفاصيل المشروع: {SelectedProject.ProjectName}";
                // سيتم تنفيذ هذا لاحقاً - فتح نافذة تفاصيل المشروع
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في عرض التفاصيل: {ex.Message}";
            }
        }

        private async Task ViewIndicatorDetailsAsync()
        {
            if (SelectedIndicator == null) return;

            try
            {
                StatusMessage = $"عرض تفاصيل المؤشر: {SelectedIndicator.IndicatorName}";
                // سيتم تنفيذ هذا لاحقاً - فتح نافذة تفاصيل المؤشر
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في عرض التفاصيل: {ex.Message}";
            }
        }

        private async Task MarkAlertAsReadAsync()
        {
            if (SelectedAlert == null) return;

            try
            {
                var success = await _meService.MarkAlertAsReadAsync(SelectedAlert.Id);
                if (success)
                {
                    SelectedAlert.IsRead = true;
                    StatusMessage = "تم تحديث التنبيه";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحديث التنبيه: {ex.Message}";
            }
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                StatusMessage = "إنشاء تقرير M&E...";
                // سيتم تنفيذ هذا لاحقاً - فتح نافذة إنشاء تقرير M&E
                await Task.Delay(100);
                StatusMessage = "جاهز لإنشاء التقرير";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إنشاء التقرير: {ex.Message}";
            }
        }

        private async Task ExportDataAsync()
        {
            try
            {
                StatusMessage = "تصدير بيانات لوحة التحكم...";
                // سيتم تنفيذ هذا لاحقاً - تصدير البيانات
                await Task.Delay(100);
                StatusMessage = "جاهز للتصدير";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التصدير: {ex.Message}";
            }
        }

        /// <summary>
        /// فتح نافذة IPTT للمشروع المحدد
        /// </summary>
        private void OpenIpttWindow()
        {
            try
            {
                if (SelectedProject == null)
                {
                    MessageBox.Show("يرجى اختيار مشروع أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // البحث عن المشروع في قاعدة البيانات
                var projectService = ProjectService.Instance;
                var project = projectService.GetAllProjects().FirstOrDefault(p => p.Name == SelectedProject.ProjectName);

                if (project == null)
                {
                    MessageBox.Show("لم يتم العثور على المشروع في قاعدة البيانات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // فتح نافذة IPTT
                var ipttWindow = new IPTTDesignWindow(project);
                ipttWindow.Owner = Application.Current.MainWindow;
                ipttWindow.ShowDialog();

                // تحديث البيانات بعد إغلاق نافذة IPTT
                _ = RefreshAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة IPTT: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            _meService?.Dispose();
        }
        #endregion
    }
}
