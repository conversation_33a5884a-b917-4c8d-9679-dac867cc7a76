# PowerShell script to build and run TYF Management System
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "بناء وتشغيل نظام TYF Management System" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "المسار الحالي: $(Get-Location)" -ForegroundColor Gray
Write-Host "مسار الملف: $PSScriptRoot" -ForegroundColor Gray

# Change to script directory first
Set-Location $PSScriptRoot
Write-Host "تم الانتقال إلى: $(Get-Location)" -ForegroundColor Gray

# Check if TYFManagementSystem directory exists
$projectPath = Join-Path $PSScriptRoot "TYFManagementSystem"
if (-not (Test-Path $projectPath)) {
    Write-Host "❌ مجلد TYFManagementSystem غير موجود!" -ForegroundColor Red
    Write-Host "المحتويات الحالية:" -ForegroundColor Yellow
    Get-ChildItem
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

# Change to project directory
Set-Location $projectPath
Write-Host "تم الانتقال إلى مجلد المشروع: $(Get-Location)" -ForegroundColor Gray

Write-Host ""
Write-Host "🔧 تنظيف المشروع..." -ForegroundColor Yellow
dotnet clean

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تنظيف المشروع!" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "📦 استعادة الحزم..." -ForegroundColor Yellow
dotnet restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في استعادة الحزم!" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "🏗️ بناء المشروع..." -ForegroundColor Yellow
dotnet build --configuration Release

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ فشل في بناء المشروع!" -ForegroundColor Red
    Write-Host "تحقق من الأخطاء أعلاه وحاول مرة أخرى." -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "✅ تم بناء المشروع بنجاح!" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host ""

dotnet run

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ فشل في تشغيل التطبيق!" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host ""
Write-Host "✅ تم إغلاق التطبيق بنجاح." -ForegroundColor Green
Read-Host "اضغط Enter للمتابعة"
