using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.Database;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة ترحيل قاعدة البيانات وإضافة الجداول الجديدة
    /// </summary>
    public static class DatabaseMigrationService
    {
        /// <summary>
        /// ترحيل قاعدة البيانات وإضافة جدول المشاريع إذا لم يكن موجوداً
        /// </summary>
        public static async Task MigrateDatabaseAsync()
        {
            try
            {
                using var context = new TyfDbContext();
                
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                await context.Database.EnsureCreatedAsync();
                
                // التحقق من وجود جدول Projects
                var tableExists = await CheckIfProjectsTableExistsAsync(context);
                
                if (!tableExists)
                {
                    await CreateProjectsTableAsync(context);
                    await MigrateExistingDataAsync(context);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في ترحيل قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من وجود جدول Projects
        /// </summary>
        private static async Task<bool> CheckIfProjectsTableExistsAsync(TyfDbContext context)
        {
            try
            {
                await context.Projects.AnyAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء جدول Projects
        /// </summary>
        private static async Task CreateProjectsTableAsync(TyfDbContext context)
        {
            var createTableSql = @"
                CREATE TABLE IF NOT EXISTS Projects (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProjectNumber NVARCHAR(50),
                    ProjectCode NVARCHAR(50),
                    Name NVARCHAR(200) NOT NULL,
                    Region NVARCHAR(100),
                    Description NVARCHAR(1000),
                    StartDate DATETIME NOT NULL,
                    EndDate DATETIME NOT NULL,
                    Status NVARCHAR(50),
                    Budget DECIMAL(18,2) NOT NULL,
                    Manager NVARCHAR(100),
                    Beneficiaries INTEGER NOT NULL,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    LastModified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                );";

            await context.Database.ExecuteSqlRawAsync(createTableSql);

            // إنشاء الفهارس
            var createIndexSql = @"
                CREATE UNIQUE INDEX IF NOT EXISTS IX_Projects_ProjectNumber ON Projects (ProjectNumber);
                CREATE INDEX IF NOT EXISTS IX_Projects_ProjectCode ON Projects (ProjectCode);
                CREATE INDEX IF NOT EXISTS IX_Projects_Status ON Projects (Status);
                CREATE INDEX IF NOT EXISTS IX_Projects_Region ON Projects (Region);";

            await context.Database.ExecuteSqlRawAsync(createIndexSql);
        }

        /// <summary>
        /// ترحيل البيانات الموجودة من الذاكرة إلى قاعدة البيانات
        /// </summary>
        private static async Task MigrateExistingDataAsync(TyfDbContext context)
        {
            try
            {
                // إضافة البيانات النموذجية إذا كان الجدول فارغاً
                var hasData = await context.Projects.AnyAsync();
                if (!hasData)
                {
                    var sampleProjects = GetSampleProjects();
                    
                    foreach (var project in sampleProjects)
                    {
                        var projectDb = new ProjectDb
                        {
                            ProjectNumber = project.ProjectNumber,
                            ProjectCode = project.ProjectCode,
                            Name = project.Name,
                            Region = project.Region,
                            Description = project.Description,
                            StartDate = project.StartDate,
                            EndDate = project.EndDate,
                            Status = project.Status,
                            Budget = project.Budget,
                            Manager = project.Manager,
                            Beneficiaries = project.Beneficiaries,
                            CreatedDate = DateTime.Now,
                            LastModified = DateTime.Now
                        };
                        
                        context.Projects.Add(projectDb);
                    }
                    
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء البيانات النموذجية
                Console.WriteLine($"تحذير: فشل في إضافة البيانات النموذجية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على البيانات النموذجية
        /// </summary>
        private static List<Project> GetSampleProjects()
        {
            return new List<Project>
            {
                new Project
                {
                    Id = 1,
                    ProjectNumber = "2024-TYF-001",
                    ProjectCode = "ORF-001",
                    Name = "مشروع كفالة الأيتام",
                    Region = "الرياض",
                    Description = "برنامج كفالة شاملة للأطفال الأيتام",
                    StartDate = new DateTime(2024, 1, 1),
                    EndDate = new DateTime(2024, 12, 31),
                    Status = "نشط",
                    Budget = 50000,
                    Manager = "أحمد محمد",
                    Beneficiaries = 150
                },
                new Project
                {
                    Id = 2,
                    ProjectNumber = "2024-TYF-002",
                    ProjectCode = "EDU-001",
                    Name = "مشروع التعليم المجتمعي",
                    Region = "جدة",
                    Description = "برنامج محو الأمية وتعليم الكبار",
                    StartDate = new DateTime(2024, 2, 1),
                    EndDate = new DateTime(2024, 11, 30),
                    Status = "نشط",
                    Budget = 30000,
                    Manager = "فاطمة علي",
                    Beneficiaries = 200
                },
                new Project
                {
                    Id = 3,
                    ProjectNumber = "2024-TYF-003",
                    ProjectCode = "REL-001",
                    Name = "مشروع الإغاثة الطارئة",
                    Region = "الدمام",
                    Description = "توزيع المساعدات الغذائية والطبية",
                    StartDate = new DateTime(2024, 3, 1),
                    EndDate = new DateTime(2024, 6, 30),
                    Status = "مكتمل",
                    Budget = 75000,
                    Manager = "محمد حسن",
                    Beneficiaries = 500
                },
                new Project
                {
                    Id = 4,
                    ProjectNumber = "2024-TYF-004",
                    ProjectCode = "TRN-001",
                    Name = "مشروع التدريب المهني",
                    Region = "مكة المكرمة",
                    Description = "تدريب الشباب على المهن والحرف",
                    StartDate = new DateTime(2024, 4, 1),
                    EndDate = new DateTime(2024, 10, 31),
                    Status = "نشط",
                    Budget = 40000,
                    Manager = "سارة أحمد",
                    Beneficiaries = 100
                },
                new Project
                {
                    Id = 5,
                    ProjectNumber = "2024-TYF-005",
                    ProjectCode = "HLT-001",
                    Name = "مشروع الرعاية الصحية",
                    Region = "المدينة المنورة",
                    Description = "توفير الخدمات الصحية للمحتاجين",
                    StartDate = new DateTime(2024, 5, 1),
                    EndDate = new DateTime(2024, 12, 31),
                    Status = "نشط",
                    Budget = 60000,
                    Manager = "خالد الأحمد",
                    Beneficiaries = 300
                },
                new Project
                {
                    Id = 6,
                    ProjectNumber = "2024-TYF-006",
                    ProjectCode = "HSG-001",
                    Name = "مشروع الإسكان الخيري",
                    Region = "الطائف",
                    Description = "توفير السكن المناسب للأسر المحتاجة",
                    StartDate = new DateTime(2024, 6, 1),
                    EndDate = new DateTime(2025, 6, 30),
                    Status = "مخطط",
                    Budget = 120000,
                    Manager = "نورا السالم",
                    Beneficiaries = 80
                }
            };
        }
    }
}
