using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TYFManagementSystem.Models.Activities
{
    /// <summary>
    /// نموذج النشاط الميداني
    /// </summary>
    public class FieldActivity
    {
        [Key]
        public string ActivityId { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(200)]
        public string ActivityName { get; set; } = "";

        [MaxLength(1000)]
        public string Description { get; set; } = "";

        [MaxLength(500)]
        public string Objectives { get; set; } = "";

        [Required]
        public string ProjectId { get; set; } = "";

        [Required]
        [MaxLength(100)]
        public string Location { get; set; } = "";

        [MaxLength(200)]
        public string LocationDetails { get; set; } = "";

        public double? Latitude { get; set; }
        public double? Longitude { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public ActivityStatus Status { get; set; } = ActivityStatus.Planned;

        public ActivityPriority Priority { get; set; } = ActivityPriority.Medium;

        [Required]
        [MaxLength(100)]
        public string ResponsiblePerson { get; set; } = "";

        [MaxLength(100)]
        public string ContactInfo { get; set; } = "";

        public int EstimatedParticipants { get; set; }
        public int ActualParticipants { get; set; }

        public decimal EstimatedBudget { get; set; }
        public decimal ActualBudget { get; set; }

        [MaxLength(1000)]
        public string Notes { get; set; } = "";

        [MaxLength(1000)]
        public string Results { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;

        [MaxLength(100)]
        public string CreatedBy { get; set; } = "";

        // Navigation properties
        public virtual List<ActivityIndicatorLink> LinkedIndicators { get; set; } = new();
        public virtual List<ActivityDocument> Documents { get; set; } = new();
        public virtual List<ActivityResult> ActivityResults { get; set; } = new();
    }

    /// <summary>
    /// ربط النشاط بالمؤشرات
    /// </summary>
    public class ActivityIndicatorLink
    {
        [Key]
        public string LinkId { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ActivityId { get; set; } = "";

        [Required]
        public string IndicatorId { get; set; } = "";

        [MaxLength(200)]
        public string IndicatorName { get; set; } = "";

        public double ExpectedContribution { get; set; } = 0;
        public double ActualContribution { get; set; } = 0;

        [MaxLength(500)]
        public string ContributionNotes { get; set; } = "";

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("ActivityId")]
        public virtual FieldActivity Activity { get; set; } = null!;
    }

    /// <summary>
    /// مستندات النشاط
    /// </summary>
    public class ActivityDocument
    {
        [Key]
        public string DocumentId { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ActivityId { get; set; } = "";

        [Required]
        [MaxLength(200)]
        public string DocumentName { get; set; } = "";

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = "";

        public DocumentType DocumentType { get; set; } = DocumentType.Other;

        [MaxLength(100)]
        public string FileSize { get; set; } = "";

        [MaxLength(50)]
        public string FileExtension { get; set; } = "";

        [MaxLength(500)]
        public string Description { get; set; } = "";

        public DateTime UploadDate { get; set; } = DateTime.Now;

        [MaxLength(100)]
        public string UploadedBy { get; set; } = "";

        // Navigation properties
        [ForeignKey("ActivityId")]
        public virtual FieldActivity Activity { get; set; } = null!;
    }

    /// <summary>
    /// نتائج النشاط
    /// </summary>
    public class ActivityResult
    {
        [Key]
        public string ResultId { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string ActivityId { get; set; } = "";

        [Required]
        [MaxLength(200)]
        public string ResultTitle { get; set; } = "";

        [MaxLength(1000)]
        public string ResultDescription { get; set; } = "";

        public double QuantitativeResult { get; set; } = 0;

        [MaxLength(50)]
        public string Unit { get; set; } = "";

        [MaxLength(1000)]
        public string QualitativeResult { get; set; } = "";

        public DateTime RecordedDate { get; set; } = DateTime.Now;

        [MaxLength(100)]
        public string RecordedBy { get; set; } = "";

        public bool IsVerified { get; set; } = false;

        [MaxLength(100)]
        public string VerifiedBy { get; set; } = "";

        public DateTime? VerificationDate { get; set; }

        // Navigation properties
        [ForeignKey("ActivityId")]
        public virtual FieldActivity Activity { get; set; } = null!;
    }

    /// <summary>
    /// ملخص الأنشطة للعرض
    /// </summary>
    public class ActivitySummary
    {
        public string ActivityId { get; set; } = "";
        public string ActivityName { get; set; } = "";
        public string ProjectName { get; set; } = "";
        public string Location { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public ActivityStatus Status { get; set; }
        public ActivityPriority Priority { get; set; }
        public string ResponsiblePerson { get; set; } = "";
        public int LinkedIndicatorsCount { get; set; }
        public int DocumentsCount { get; set; }
        public double ProgressPercentage { get; set; }
        public string StatusText { get; set; } = "";
        public string PriorityText { get; set; } = "";
    }

    /// <summary>
    /// بيانات التقويم
    /// </summary>
    public class CalendarActivityData
    {
        public string ActivityId { get; set; } = "";
        public string ActivityName { get; set; } = "";
        public string ProjectName { get; set; } = "";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public ActivityStatus Status { get; set; }
        public ActivityPriority Priority { get; set; }
        public string Location { get; set; } = "";
        public string ResponsiblePerson { get; set; } = "";
        public string StatusColor { get; set; } = "";
        public string PriorityColor { get; set; } = "";
        public bool IsMultiDay => (EndDate.Date - StartDate.Date).Days > 0;
        public int DurationDays => (EndDate.Date - StartDate.Date).Days + 1;
    }

    /// <summary>
    /// حالة النشاط
    /// </summary>
    public enum ActivityStatus
    {
        Planned = 1,        // مخطط
        InProgress = 2,     // جاري التنفيذ
        Completed = 3,      // مكتمل
        Cancelled = 4,      // ملغي
        Postponed = 5,      // مؤجل
        OnHold = 6         // متوقف مؤقتاً
    }

    /// <summary>
    /// أولوية النشاط
    /// </summary>
    public enum ActivityPriority
    {
        Low = 1,        // منخفضة
        Medium = 2,     // متوسطة
        High = 3,       // عالية
        Critical = 4    // حرجة
    }

    /// <summary>
    /// نوع المستند
    /// </summary>
    public enum DocumentType
    {
        Photo = 1,          // صورة
        Report = 2,         // تقرير
        Presentation = 3,   // عرض تقديمي
        Spreadsheet = 4,    // جدول بيانات
        Document = 5,       // مستند
        Video = 6,          // فيديو
        Audio = 7,          // صوت
        Other = 8          // أخرى
    }

    /// <summary>
    /// فلاتر البحث
    /// </summary>
    public class ActivitySearchFilters
    {
        public string? SearchText { get; set; }
        public string? ProjectId { get; set; }
        public string? Location { get; set; }
        public ActivityStatus? Status { get; set; }
        public ActivityPriority? Priority { get; set; }
        public DateTime? StartDateFrom { get; set; }
        public DateTime? StartDateTo { get; set; }
        public DateTime? EndDateFrom { get; set; }
        public DateTime? EndDateTo { get; set; }
        public string? ResponsiblePerson { get; set; }
        public bool? HasLinkedIndicators { get; set; }
        public bool? HasDocuments { get; set; }
    }
}
