using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.Activities;
using TYFManagementSystem.Services.Activities;

namespace TYFManagementSystem.ViewModels.Activities
{
    /// <summary>
    /// ViewModel لتقويم الأنشطة
    /// </summary>
    public class ActivityCalendarViewModel : BaseViewModel, IDisposable
    {
        #region Private Fields
        private readonly ActivityService _activityService;
        private DateTime _currentMonth;
        private ObservableCollection<CalendarDay> _calendarDays;
        private ObservableCollection<CalendarActivityData> _monthActivities;
        private CalendarActivityData? _selectedActivity;
        private bool _isLoading;
        private string _statusMessage = "";
        #endregion

        #region Properties
        public DateTime CurrentMonth
        {
            get => _currentMonth;
            set
            {
                if (SetProperty(ref _currentMonth, value))
                {
                    _ = LoadMonthDataAsync();
                }
            }
        }

        public ObservableCollection<CalendarDay> CalendarDays
        {
            get => _calendarDays;
            set => SetProperty(ref _calendarDays, value);
        }

        public ObservableCollection<CalendarActivityData> MonthActivities
        {
            get => _monthActivities;
            set => SetProperty(ref _monthActivities, value);
        }

        public CalendarActivityData? SelectedActivity
        {
            get => _selectedActivity;
            set => SetProperty(ref _selectedActivity, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string CurrentMonthText => CurrentMonth.ToString("MMMM yyyy", new CultureInfo("ar-SA"));
        public string TodayText => DateTime.Today.ToString("dd/MM/yyyy");
        #endregion

        #region Commands
        public ICommand PreviousMonthCommand { get; private set; }
        public ICommand NextMonthCommand { get; private set; }
        public ICommand TodayCommand { get; private set; }
        public ICommand CreateActivityCommand { get; private set; }
        public ICommand EditActivityCommand { get; private set; }
        public ICommand ViewActivityDetailsCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        #endregion

        #region Constructor
        public ActivityCalendarViewModel()
        {
            _activityService = new ActivityService();
            _currentMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            _calendarDays = new ObservableCollection<CalendarDay>();
            _monthActivities = new ObservableCollection<CalendarActivityData>();

            InitializeCommands();
            _ = LoadMonthDataAsync();
        }
        #endregion

        #region Private Methods
        private void InitializeCommands()
        {
            PreviousMonthCommand = new RelayCommand(() => CurrentMonth = CurrentMonth.AddMonths(-1));
            NextMonthCommand = new RelayCommand(() => CurrentMonth = CurrentMonth.AddMonths(1));
            TodayCommand = new RelayCommand(() => CurrentMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1));
            CreateActivityCommand = new RelayCommand(async () => await CreateActivityAsync());
            EditActivityCommand = new RelayCommand(async () => await EditActivityAsync(), () => SelectedActivity != null);
            ViewActivityDetailsCommand = new RelayCommand(async () => await ViewActivityDetailsAsync(), () => SelectedActivity != null);
            RefreshCommand = new RelayCommand(async () => await LoadMonthDataAsync());
        }

        private async Task LoadMonthDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل بيانات التقويم...";

                // تحديد نطاق الشهر
                var startDate = CurrentMonth;
                var endDate = CurrentMonth.AddMonths(1).AddDays(-1);

                // تحميل الأنشطة للشهر
                var activities = await _activityService.GetCalendarDataAsync(startDate, endDate);

                MonthActivities.Clear();
                foreach (var activity in activities)
                {
                    MonthActivities.Add(activity);
                }

                // إنشاء أيام التقويم
                GenerateCalendarDays();

                StatusMessage = $"تم تحميل {activities.Count} نشاط للشهر";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void GenerateCalendarDays()
        {
            CalendarDays.Clear();

            // الحصول على أول يوم في الشهر
            var firstDayOfMonth = CurrentMonth;
            var lastDayOfMonth = CurrentMonth.AddMonths(1).AddDays(-1);

            // الحصول على أول يوم أحد في الأسبوع الذي يحتوي على أول يوم في الشهر
            var startDate = firstDayOfMonth;
            while (startDate.DayOfWeek != DayOfWeek.Sunday)
            {
                startDate = startDate.AddDays(-1);
            }

            // إنشاء 42 يوم (6 أسابيع × 7 أيام)
            for (int i = 0; i < 42; i++)
            {
                var currentDate = startDate.AddDays(i);
                var isCurrentMonth = currentDate.Month == CurrentMonth.Month;
                var isToday = currentDate.Date == DateTime.Today;

                // الحصول على الأنشطة لهذا اليوم
                var dayActivities = MonthActivities
                    .Where(a => a.StartDate.Date <= currentDate.Date && a.EndDate.Date >= currentDate.Date)
                    .ToList();

                var calendarDay = new CalendarDay
                {
                    Date = currentDate,
                    IsCurrentMonth = isCurrentMonth,
                    IsToday = isToday,
                    Activities = new ObservableCollection<CalendarActivityData>(dayActivities),
                    HasActivities = dayActivities.Any()
                };

                CalendarDays.Add(calendarDay);
            }
        }

        private async Task CreateActivityAsync()
        {
            try
            {
                StatusMessage = "إنشاء نشاط جديد...";
                // سيتم فتح نافذة إنشاء النشاط
                await Task.Delay(100);
                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إنشاء النشاط: {ex.Message}";
            }
        }

        private async Task EditActivityAsync()
        {
            if (SelectedActivity == null) return;

            try
            {
                StatusMessage = $"تعديل النشاط: {SelectedActivity.ActivityName}";
                // سيتم فتح نافذة تعديل النشاط
                await Task.Delay(100);
                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تعديل النشاط: {ex.Message}";
            }
        }

        private async Task ViewActivityDetailsAsync()
        {
            if (SelectedActivity == null) return;

            try
            {
                StatusMessage = $"عرض تفاصيل النشاط: {SelectedActivity.ActivityName}";
                // سيتم فتح نافذة تفاصيل النشاط
                await Task.Delay(100);
                StatusMessage = "جاهز";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في عرض التفاصيل: {ex.Message}";
            }
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            _activityService?.Dispose();
        }
        #endregion
    }

    /// <summary>
    /// نموذج يوم في التقويم
    /// </summary>
    public class CalendarDay : BaseViewModel
    {
        private DateTime _date;
        private bool _isCurrentMonth;
        private bool _isToday;
        private bool _hasActivities;
        private ObservableCollection<CalendarActivityData> _activities;

        public DateTime Date
        {
            get => _date;
            set => SetProperty(ref _date, value);
        }

        public bool IsCurrentMonth
        {
            get => _isCurrentMonth;
            set => SetProperty(ref _isCurrentMonth, value);
        }

        public bool IsToday
        {
            get => _isToday;
            set => SetProperty(ref _isToday, value);
        }

        public bool HasActivities
        {
            get => _hasActivities;
            set => SetProperty(ref _hasActivities, value);
        }

        public ObservableCollection<CalendarActivityData> Activities
        {
            get => _activities;
            set => SetProperty(ref _activities, value);
        }

        public string DayNumber => Date.Day.ToString();
        public string DayName => Date.ToString("ddd", new CultureInfo("ar-SA"));

        public CalendarDay()
        {
            _activities = new ObservableCollection<CalendarActivityData>();
        }
    }
}
