using System;
using System.ComponentModel.DataAnnotations;

namespace TYFManagementSystem.Models.Reports
{
    /// <summary>
    /// نموذج التقرير الأساسي
    /// </summary>
    public class Report
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = "";

        [Required]
        public ReportType Type { get; set; }

        public string Description { get; set; } = "";

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastModified { get; set; }

        [MaxLength(100)]
        public string CreatedBy { get; set; } = "النظام";

        public string Notes { get; set; } = "";

        /// <summary>
        /// معايير التقرير (JSON)
        /// </summary>
        public string Criteria { get; set; } = "";

        /// <summary>
        /// بيانات التقرير (JSON)
        /// </summary>
        public string Data { get; set; } = "";

        /// <summary>
        /// حالة التقرير
        /// </summary>
        public ReportStatus Status { get; set; } = ReportStatus.Draft;

        /// <summary>
        /// معرف المشروع المرتبط (اختياري)
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// تاريخ البداية للبيانات
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// تاريخ النهاية للبيانات
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// المواقع المشمولة (JSON array)
        /// </summary>
        public string IncludedLocations { get; set; } = "";

        /// <summary>
        /// حجم التقرير بالبايت
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// عدد السجلات في التقرير
        /// </summary>
        public int RecordCount { get; set; }
    }

    /// <summary>
    /// أنواع التقارير
    /// </summary>
    public enum ReportType
    {
        IPTT = 1,           // تقارير IPTT
        Financial = 2,      // تقارير مالية
        Activities = 3,     // تقارير الأنشطة
        Projects = 4,       // تقارير المشاريع
        Custom = 5,         // تقارير مخصصة
        Summary = 6,        // تقارير ملخصة
        Detailed = 7        // تقارير مفصلة
    }

    /// <summary>
    /// حالات التقرير
    /// </summary>
    public enum ReportStatus
    {
        Draft = 1,          // مسودة
        Generated = 2,      // تم إنشاؤه
        Exported = 3,       // تم تصديره
        Archived = 4        // مؤرشف
    }

    /// <summary>
    /// صيغ التصدير المدعومة
    /// </summary>
    public enum ExportFormat
    {
        PDF = 1,
        Excel = 2,
        CSV = 3,
        JSON = 4,
        XML = 5
    }
}
