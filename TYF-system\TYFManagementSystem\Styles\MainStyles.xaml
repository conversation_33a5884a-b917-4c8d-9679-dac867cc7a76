<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Colors -->
    <SolidColorBrush x:Key="PrimaryColor" Color="#2E7D32"/>
    <SolidColorBrush x:Key="SecondaryColor" Color="#4CAF50"/>
    <SolidColorBrush x:Key="AccentColor" Color="#81C784"/>
    <SolidColorBrush x:Key="BackgroundColor" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="SurfaceColor" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextPrimaryColor" Color="#212121"/>
    <SolidColorBrush x:Key="TextSecondaryColor" Color="#757575"/>
    <SolidColorBrush x:Key="BorderColor" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="HoverColor" Color="#66BB6A"/>
    <SolidColorBrush x:Key="PressedColor" Color="#388E3C"/>

    <!-- Fonts -->
    <FontFamily x:Key="PrimaryFont">Segoe UI</FontFamily>
    <FontFamily x:Key="ArabicFont">Tahoma</FontFamily>

    <!-- Button Styles -->
    <Style x:Key="SidebarButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,15"/>
        <Setter Property="Margin" Value="0,2"/>
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                        <Grid>
                            <Rectangle x:Name="hoverRect" 
                                     Fill="{StaticResource HoverColor}" 
                                     Opacity="0" 
                                     RadiusX="5" 
                                     RadiusY="5"/>
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="hoverRect" Property="Opacity" Value="0.1"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="hoverRect" Property="Fill" Value="{StaticResource PressedColor}"/>
                            <Setter TargetName="hoverRect" Property="Opacity" Value="0.2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Selected Button Style -->
    <Style x:Key="SelectedSidebarButtonStyle" TargetType="Button" BasedOn="{StaticResource SidebarButtonStyle}">
        <Setter Property="Background" Value="{StaticResource AccentColor}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
    </Style>

    <!-- Card Style -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceColor}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Header Text Style -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>

    <!-- Subtitle Text Style -->
    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryColor}"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

</ResourceDictionary>
