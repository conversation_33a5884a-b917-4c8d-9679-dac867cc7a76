<UserControl x:Class="TYFManagementSystem.Views.Activities.ActivityCalendarView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:Converters="clr-namespace:TYFManagementSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- تنسيق يوم التقويم -->
        <Style x:Key="CalendarDayStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="1"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsCurrentMonth}" Value="False">
                    <Setter Property="Background" Value="#F5F5F5"/>
                    <Setter Property="Opacity" Value="0.6"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding IsToday}" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding HasActivities}" Value="True">
                    <Setter Property="Background" Value="#FFF3E0"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تنسيق النشاط في التقويم -->
        <Style x:Key="CalendarActivityStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Background" Value="{Binding StatusColor}"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والتنقل -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📅" FontSize="28" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="تقويم الأنشطة الميدانية" 
                                 FontSize="22" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock Text="{Binding CurrentMonthText}" 
                                 FontSize="16" 
                                 Foreground="White"
                                 Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="◀"
                            Command="{Binding PreviousMonthCommand}"
                            Background="White"
                            Foreground="#1976D2"
                            Padding="15,8"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="اليوم"
                            Command="{Binding TodayCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="▶"
                            Command="{Binding NextMonthCommand}"
                            Background="White"
                            Foreground="#1976D2"
                            Padding="15,8"
                            FontSize="14"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="➕ نشاط جديد"
                            Command="{Binding CreateActivityCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Background="White"
                            Foreground="#1976D2"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- رؤوس أيام الأسبوع -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="0" Margin="0,0,0,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="الأحد" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
                <TextBlock Grid.Column="1" Text="الاثنين" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
                <TextBlock Grid.Column="2" Text="الثلاثاء" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
                <TextBlock Grid.Column="3" Text="الأربعاء" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
                <TextBlock Grid.Column="4" Text="الخميس" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
                <TextBlock Grid.Column="5" Text="الجمعة" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
                <TextBlock Grid.Column="6" Text="السبت" HorizontalAlignment="Center" Padding="10" FontWeight="Bold"/>
            </Grid>
        </Border>

        <!-- شبكة التقويم -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="1">
            <ItemsControl ItemsSource="{Binding CalendarDays}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <UniformGrid Columns="7" Rows="6"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource CalendarDayStyle}" MinHeight="100">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- رقم اليوم -->
                                <TextBlock Grid.Row="0" 
                                         Text="{Binding DayNumber}" 
                                         HorizontalAlignment="Right"
                                         VerticalAlignment="Top"
                                         FontWeight="Bold"
                                         FontSize="12"
                                         Margin="5,2">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsToday}" Value="True">
                                                    <Setter Property="Foreground" Value="#2196F3"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsCurrentMonth}" Value="False">
                                                    <Setter Property="Foreground" Value="#999"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>

                                <!-- الأنشطة -->
                                <ScrollViewer Grid.Row="1" 
                                            VerticalScrollBarVisibility="Auto"
                                            HorizontalScrollBarVisibility="Disabled"
                                            Margin="2">
                                    <ItemsControl ItemsSource="{Binding Activities}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Style="{StaticResource CalendarActivityStyle}">
                                                    <TextBlock Text="{Binding ActivityName}" 
                                                             Foreground="White"
                                                             FontSize="9"
                                                             FontWeight="Bold"
                                                             TextTrimming="CharacterEllipsis"
                                                             ToolTip="{Binding ActivityName}"/>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </Border>

        <!-- شريط الحالة -->
        <Border Grid.Row="3" Background="#E0E0E0" Padding="15,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"
                           FontSize="11"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding TodayText, StringFormat=اليوم: {0}}" 
                               VerticalAlignment="Center"
                               FontSize="11"
                               Margin="10,0"/>
                    
                    <TextBlock Text="{Binding MonthActivities.Count, StringFormat=الأنشطة: {0}}" 
                               VerticalAlignment="Center"
                               FontSize="11"
                               Margin="10,0"/>
                    
                    <ProgressBar Width="100" 
                                 Height="15"
                                 IsIndeterminate="{Binding IsLoading}"
                                 Visibility="{Binding IsLoading, Converter={x:Static Converters:BoolToVisibilityConverter.Instance}}"
                                 Margin="10,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
