using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Models;

namespace TYFManagementSystem.Data
{
    public class TYFDbContext : DbContext
    {
        public TYFDbContext(DbContextOptions<TYFDbContext> options) : base(options)
        {
        }

        // DbSets for the application entities
        public DbSet<User> Users { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<Beneficiary> Beneficiaries { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Use SQLite for simplicity - local database file
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TYFDatabase.db");
                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure entity relationships and constraints here
            // This will be expanded as we add models
        }
    }
}
