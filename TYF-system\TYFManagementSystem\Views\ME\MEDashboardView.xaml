<UserControl x:Class="TYFManagementSystem.Views.ME.MEDashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:Converters="clr-namespace:TYFManagementSystem.Converters"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- تحويل حالة المؤشر إلى لون -->
        <Style x:Key="IndicatorStatusStyle" TargetType="Border">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="OnTrack">
                    <Setter Property="Background" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="BehindSchedule">
                    <Setter Property="Background" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="AtRisk">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل أولوية التنبيه إلى لون -->
        <Style x:Key="AlertPriorityStyle" TargetType="Border">
            <Setter Property="Width" Value="4"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Priority}" Value="Low">
                    <Setter Property="Background" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="Medium">
                    <Setter Property="Background" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="High">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="Critical">
                    <Setter Property="Background" Value="#9C27B0"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="28" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="نظام متابعة وتقييم (M&amp;E)" 
                                 FontSize="22" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock Text="لوحة تحكم شاملة لمتابعة أداء المشاريع والمؤشرات" 
                                 FontSize="13" 
                                 Foreground="White"
                                 Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Background="White"
                            Foreground="#1976D2"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="📊 تقرير M&amp;E"
                            Command="{Binding GenerateReportCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>

                    <Button Content="📈 فتح IPTT"
                            Command="{Binding OpenIpttCommand}"
                            Background="#FF9800"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإحصائيات العامة -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إجمالي المشاريع -->
                <Border Grid.Column="0" Background="#E3F2FD" Padding="15" Margin="5">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📁" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OverallStats.TotalProjects}" 
                                 FontSize="28" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="#1976D2"/>
                        <TextBlock Text="إجمالي المشاريع" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"
                                 Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- المشاريع النشطة -->
                <Border Grid.Column="1" Background="#E8F5E8" Padding="15" Margin="5">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🟢" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OverallStats.ActiveProjects}" 
                                 FontSize="28" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="#4CAF50"/>
                        <TextBlock Text="مشاريع نشطة" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"
                                 Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- إجمالي المؤشرات -->
                <Border Grid.Column="2" Background="#FFF3E0" Padding="15" Margin="5">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📈" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OverallStats.TotalIndicators}" 
                                 FontSize="28" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="#FF9800"/>
                        <TextBlock Text="إجمالي المؤشرات" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"
                                 Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- مؤشرات على المسار -->
                <Border Grid.Column="3" Background="#E8F5E8" Padding="15" Margin="5">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OverallStats.OnTrackIndicators}" 
                                 FontSize="28" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="#4CAF50"/>
                        <TextBlock Text="على المسار" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"
                                 Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- التقدم العام -->
                <Border Grid.Column="4" Background="#F3E5F5" Padding="15" Margin="5">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OverallStats.OverallProgress, StringFormat={}{0:F1}%}" 
                                 FontSize="28" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="#9C27B0"/>
                        <TextBlock Text="التقدم العام" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"
                                 Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- المستفيدون -->
                <Border Grid.Column="5" Background="#E0F2F1" Padding="15" Margin="5">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding OverallStats.TotalBeneficiaries}" 
                                 FontSize="28" 
                                 FontWeight="Bold" 
                                 HorizontalAlignment="Center"
                                 Foreground="#009688"/>
                        <TextBlock Text="المستفيدون" 
                                 FontSize="12" 
                                 HorizontalAlignment="Center"
                                 Foreground="#666"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الجانب الأيسر - المشاريع والمؤشرات -->
            <Grid Grid.Column="0" Margin="0,0,10,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- فلاتر -->
                <Border Grid.Row="0" Background="White" Padding="15" Margin="0,0,0,10">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="فلترة البيانات:" VerticalAlignment="Center" FontWeight="Bold" Margin="0,0,15,0"/>
                        
                        <TextBlock Text="الفترة الزمنية:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox ItemsSource="{Binding TimeFilterOptions}"
                                  SelectedItem="{Binding SelectedTimeFilter}"
                                  Width="120" Margin="0,0,15,0"/>
                        
                        <TextBlock Text="الحالة:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <ComboBox ItemsSource="{Binding StatusFilterOptions}"
                                  SelectedItem="{Binding SelectedStatusFilter}"
                                  Width="120" Margin="0,0,15,0"/>
                        
                        <Button Content="📤 تصدير"
                                Command="{Binding ExportDataCommand}"
                                Background="#2196F3"
                                Foreground="White"
                                Padding="10,5"
                                FontSize="11"
                                BorderThickness="0"/>
                    </StackPanel>
                </Border>

                <!-- تبويبات البيانات -->
                <TabControl Grid.Row="1" Background="White">
                    <TabItem Header="📁 المشاريع">
                        <DataGrid ItemsSource="{Binding ProjectSummaries}"
                                  SelectedItem="{Binding SelectedProject}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  SelectionMode="Single"
                                  FontSize="11"
                                  AlternatingRowBackground="#F9F9F9">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المشروع" 
                                                  Binding="{Binding ProjectName}" 
                                                  Width="2*"/>
                                
                                <DataGridTemplateColumn Header="نسبة الإنجاز" Width="*">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <ProgressBar Value="{Binding CompletionPercentage}" 
                                                           Maximum="100" 
                                                           Width="60" 
                                                           Height="15"
                                                           Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding CompletionPercentage, StringFormat={}{0:F1}%}" 
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                
                                <DataGridTextColumn Header="المؤشرات" 
                                                  Binding="{Binding TotalIndicators}" 
                                                  Width="Auto"/>
                                
                                <DataGridTextColumn Header="الأنشطة" 
                                                  Binding="{Binding CompletedActivities}" 
                                                  Width="Auto"/>
                                
                                <DataGridTextColumn Header="المواقع" 
                                                  Binding="{Binding ActiveLocations}" 
                                                  Width="Auto"/>
                                
                                <DataGridTextColumn Header="مدير المشروع" 
                                                  Binding="{Binding ProjectManager}" 
                                                  Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </TabItem>

                    <TabItem Header="📈 المؤشرات">
                        <DataGrid ItemsSource="{Binding IndicatorProgress}"
                                  SelectedItem="{Binding SelectedIndicator}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  SelectionMode="Single"
                                  FontSize="11"
                                  AlternatingRowBackground="#F9F9F9">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم المؤشر" 
                                                  Binding="{Binding IndicatorNumber}" 
                                                  Width="Auto"/>
                                
                                <DataGridTextColumn Header="اسم المؤشر" 
                                                  Binding="{Binding IndicatorName}" 
                                                  Width="2*"/>
                                
                                <DataGridTextColumn Header="المشروع" 
                                                  Binding="{Binding ProjectName}" 
                                                  Width="*"/>
                                
                                <DataGridTemplateColumn Header="الحالة" Width="Auto">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border Style="{StaticResource IndicatorStatusStyle}">
                                                <TextBlock Foreground="White" FontSize="10" FontWeight="Bold">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Status}" Value="OnTrack">
                                                                    <Setter Property="Text" Value="على المسار"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="BehindSchedule">
                                                                    <Setter Property="Text" Value="متأخر"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="AtRisk">
                                                                    <Setter Property="Text" Value="في خطر"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                
                                <DataGridTextColumn Header="الإنجاز" 
                                                  Binding="{Binding Achievement}" 
                                                  Width="Auto"/>
                                
                                <DataGridTextColumn Header="الهدف" 
                                                  Binding="{Binding Target}" 
                                                  Width="Auto"/>
                                
                                <DataGridTextColumn Header="النسبة" 
                                                  Binding="{Binding PercentageAchieved, StringFormat={}{0:F1}%}" 
                                                  Width="Auto"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </TabItem>
                </TabControl>
            </Grid>

            <!-- الجانب الأيمن - التنبيهات -->
            <Border Grid.Column="1" Background="White" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="🔔 التنبيهات والتحديثات" 
                             FontSize="16" FontWeight="Bold" 
                             Margin="0,0,0,15"/>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding Alerts}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F9F9F9" 
                                          Margin="0,0,0,10" 
                                          Padding="0"
                                          BorderBrush="#E0E0E0" 
                                          BorderThickness="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- شريط الأولوية -->
                                            <Border Grid.Column="0" Style="{StaticResource AlertPriorityStyle}"/>

                                            <!-- محتوى التنبيه -->
                                            <StackPanel Grid.Column="1" Margin="10">
                                                <TextBlock Text="{Binding Title}" 
                                                         FontWeight="Bold" 
                                                         FontSize="12"
                                                         Margin="0,0,0,5"/>
                                                
                                                <TextBlock Text="{Binding Description}" 
                                                         FontSize="11"
                                                         TextWrapping="Wrap"
                                                         Foreground="#666"
                                                         Margin="0,0,0,5"/>
                                                
                                                <TextBlock Text="{Binding ProjectName}" 
                                                         FontSize="10"
                                                         Foreground="#999"
                                                         Margin="0,0,0,5"/>
                                                
                                                <TextBlock Text="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                                         FontSize="10"
                                                         Foreground="#999"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- شريط الحالة -->
        <Border Grid.Row="3" Background="#E0E0E0" Padding="15,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"
                           FontSize="11"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding LastUpdated, StringFormat=آخر تحديث: dd/MM/yyyy HH:mm}" 
                               VerticalAlignment="Center"
                               FontSize="11"
                               Margin="10,0"/>
                    
                    <ProgressBar Width="100" 
                                 Height="15"
                                 IsIndeterminate="{Binding IsLoading}"
                                 Visibility="{Binding IsLoading, Converter={x:Static Converters:BoolToVisibilityConverter.Instance}}"
                                 Margin="10,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
