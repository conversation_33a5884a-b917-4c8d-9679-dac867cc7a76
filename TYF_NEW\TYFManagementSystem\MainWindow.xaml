<Window x:Class="TYFManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TYFManagementSystem"
        mc:Ignorable="d"
        Title="TYF Management System"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen">

    <Grid Background="#F5F5F5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0" Background="#263238">
            <StackPanel>
                <!-- Logo/Title -->
                <Border Background="#1976D2" Padding="15" Height="60">
                    <TextBlock Text="TYF System"
                               Foreground="White"
                               FontSize="16"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               HorizontalAlignment="Center"/>
                </Border>

                <!-- Menu Items -->
                <StackPanel Margin="5">
                    <Button Content="Dashboard" Foreground="White" Background="Transparent"
                            BorderThickness="0" Padding="10" Margin="5" Height="40"/>
                    <Button Content="Projects" Foreground="White" Background="Transparent"
                            BorderThickness="0" Padding="10" Margin="5" Height="40"/>
                    <Button Content="Beneficiaries" Foreground="White" Background="Transparent"
                            BorderThickness="0" Padding="10" Margin="5" Height="40"/>
                    <Button Content="Users" Foreground="White" Background="Transparent"
                            BorderThickness="0" Padding="10" Margin="5" Height="40"/>
                    <Button Content="Settings" Foreground="White" Background="Transparent"
                            BorderThickness="0" Padding="10" Margin="5" Height="40"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Border Margin="20" Background="White" CornerRadius="5" BorderBrush="#E0E0E0" BorderThickness="1">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="مرحباً بكم في نظام إدارة مؤسسة تمدين شباب"
                               FontSize="24" FontWeight="Bold"
                               HorizontalAlignment="Center" Margin="0,0,0,15"
                               Foreground="#2196F3" TextWrapping="Wrap"/>
                    <TextBlock Text="Welcome to TYF Management System"
                               FontSize="20"
                               HorizontalAlignment="Center"
                               Foreground="#666666" Margin="0,0,0,15"/>
                    <TextBlock Text="النظام جاهز للاستخدام - System Ready"
                               FontSize="14"
                               HorizontalAlignment="Center"
                               Foreground="#4CAF50"/>
                    <Button Content="اختبار النظام - Test System"
                            Background="#2196F3" Foreground="White"
                            Padding="20,10" Margin="0,20,0,0"
                            BorderThickness="0" FontSize="14"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
