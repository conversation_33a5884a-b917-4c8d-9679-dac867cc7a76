<UserControl x:Class="TYFManagementSystem.Views.Activities.ActivitiesManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:Converters="clr-namespace:TYFManagementSystem.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- تحويل حالة النشاط إلى لون -->
        <Style x:Key="ActivityStatusStyle" TargetType="Border">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Planned">
                    <Setter Property="Background" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="InProgress">
                    <Setter Property="Background" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Completed">
                    <Setter Property="Background" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="Postponed">
                    <Setter Property="Background" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Status}" Value="OnHold">
                    <Setter Property="Background" Value="#607D8B"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- تحويل أولوية النشاط إلى لون -->
        <Style x:Key="ActivityPriorityStyle" TargetType="Border">
            <Setter Property="Width" Value="4"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Priority}" Value="Low">
                    <Setter Property="Background" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="Medium">
                    <Setter Property="Background" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="High">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Priority}" Value="Critical">
                    <Setter Property="Background" Value="#9C27B0"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="#1976D2" Padding="20" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📅" FontSize="28" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة الأنشطة الميدانية" 
                                 FontSize="22" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock Text="تخطيط وجدولة ومتابعة الأنشطة الميدانية للمشاريع" 
                                 FontSize="13" 
                                 Foreground="White"
                                 Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="➕ نشاط جديد"
                            Command="{Binding CreateNewActivityCommand}"
                            Background="#4CAF50"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="📅 عرض التقويم"
                            Command="{Binding ShowCalendarViewCommand}"
                            Background="#FF9800"
                            Foreground="White"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                    
                    <Button Content="🔄 تحديث"
                            Command="{Binding RefreshCommand}"
                            Background="White"
                            Foreground="#1976D2"
                            Padding="15,8"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- شريط البحث والفلاتر -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول - البحث والفلاتر الأساسية -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                             Width="200"
                             Padding="8"
                             FontSize="12"
                             Margin="0,0,15,0">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                                    <VisualBrush.Visual>
                                                        <TextBlock Text="البحث في الأنشطة..." 
                                                                 Foreground="Gray" 
                                                                 FontSize="12"/>
                                                    </VisualBrush.Visual>
                                                </VisualBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <TextBlock Text="المشروع:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox ItemsSource="{Binding ProjectFilterOptions}"
                              SelectedItem="{Binding SelectedProjectFilter}"
                              Width="120" Margin="0,0,15,0"/>

                    <TextBlock Text="الحالة:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox ItemsSource="{Binding StatusFilterOptions}"
                              SelectedItem="{Binding SelectedStatusFilter}"
                              Width="120" Margin="0,0,15,0"/>

                    <TextBlock Text="الأولوية:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox ItemsSource="{Binding PriorityFilterOptions}"
                              SelectedItem="{Binding SelectedPriorityFilter}"
                              Width="120" Margin="0,0,15,0"/>
                </StackPanel>

                <!-- الصف الثاني - فلاتر التاريخ والأزرار -->
                <StackPanel Grid.Row="1" Orientation="Horizontal">
                    <TextBlock Text="📅" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    
                    <TextBlock Text="من تاريخ:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <DatePicker SelectedDate="{Binding StartDateFilter}"
                                Width="120" Margin="0,0,15,0"/>

                    <TextBlock Text="إلى تاريخ:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <DatePicker SelectedDate="{Binding EndDateFilter}"
                                Width="120" Margin="0,0,15,0"/>

                    <Button Content="🗑️ مسح الفلاتر"
                            Command="{Binding ClearFiltersCommand}"
                            Background="#757575"
                            Foreground="White"
                            Padding="10,5"
                            FontSize="11"
                            BorderThickness="0"
                            Margin="15,0,5,0"/>

                    <Button Content="📤 تصدير"
                            Command="{Binding ExportActivitiesCommand}"
                            Background="#2196F3"
                            Foreground="White"
                            Padding="10,5"
                            FontSize="11"
                            BorderThickness="0"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول الأنشطة -->
        <Border Grid.Row="2" Background="White" Padding="0">
            <DataGrid ItemsSource="{Binding FilteredActivities}"
                      SelectedItem="{Binding SelectedActivity}"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      FontSize="11"
                      AlternatingRowBackground="#F9F9F9">
                
                <DataGrid.Columns>
                    <!-- شريط الأولوية -->
                    <DataGridTemplateColumn Header="" Width="10">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource ActivityPriorityStyle}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- اسم النشاط -->
                    <DataGridTextColumn Header="اسم النشاط" 
                                      Binding="{Binding ActivityName}" 
                                      Width="2*"/>

                    <!-- المشروع -->
                    <DataGridTextColumn Header="المشروع" 
                                      Binding="{Binding ProjectName}" 
                                      Width="*"/>

                    <!-- الموقع -->
                    <DataGridTextColumn Header="الموقع" 
                                      Binding="{Binding Location}" 
                                      Width="*"/>

                    <!-- تاريخ البداية -->
                    <DataGridTextColumn Header="تاريخ البداية" 
                                      Binding="{Binding StartDate, StringFormat=dd/MM/yyyy}" 
                                      Width="Auto"/>

                    <!-- تاريخ النهاية -->
                    <DataGridTextColumn Header="تاريخ النهاية" 
                                      Binding="{Binding EndDate, StringFormat=dd/MM/yyyy}" 
                                      Width="Auto"/>

                    <!-- الحالة -->
                    <DataGridTemplateColumn Header="الحالة" Width="Auto">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource ActivityStatusStyle}">
                                    <TextBlock Text="{Binding StatusText}" 
                                             Foreground="White" 
                                             FontSize="10" 
                                             FontWeight="Bold"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- الأولوية -->
                    <DataGridTextColumn Header="الأولوية" 
                                      Binding="{Binding PriorityText}" 
                                      Width="Auto"/>

                    <!-- المسؤول -->
                    <DataGridTextColumn Header="المسؤول" 
                                      Binding="{Binding ResponsiblePerson}" 
                                      Width="*"/>

                    <!-- المؤشرات المرتبطة -->
                    <DataGridTextColumn Header="المؤشرات" 
                                      Binding="{Binding LinkedIndicatorsCount}" 
                                      Width="Auto"/>

                    <!-- المستندات -->
                    <DataGridTextColumn Header="المستندات" 
                                      Binding="{Binding DocumentsCount}" 
                                      Width="Auto"/>

                    <!-- نسبة التقدم -->
                    <DataGridTemplateColumn Header="التقدم" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <ProgressBar Value="{Binding ProgressPercentage}" 
                                               Maximum="100" 
                                               Width="60" 
                                               Height="15"
                                               Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding ProgressPercentage, StringFormat={}{0:F0}%}" 
                                             VerticalAlignment="Center"
                                             FontSize="10"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="👁️"
                                            Command="{Binding DataContext.ViewActivityDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            ToolTip="عرض التفاصيل"
                                            Background="#2196F3"
                                            Foreground="White"
                                            Padding="5,2"
                                            FontSize="10"
                                            BorderThickness="0"
                                            Margin="2"/>
                                    
                                    <Button Content="✏️"
                                            Command="{Binding DataContext.EditActivityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            ToolTip="تعديل"
                                            Background="#FF9800"
                                            Foreground="White"
                                            Padding="5,2"
                                            FontSize="10"
                                            BorderThickness="0"
                                            Margin="2"/>
                                    
                                    <Button Content="🗺️"
                                            Command="{Binding DataContext.ShowOnMapCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            ToolTip="عرض على الخريطة"
                                            Background="#4CAF50"
                                            Foreground="White"
                                            Padding="5,2"
                                            FontSize="10"
                                            BorderThickness="0"
                                            Margin="2"/>
                                    
                                    <Button Content="🗑️"
                                            Command="{Binding DataContext.DeleteActivityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                            ToolTip="حذف"
                                            Background="#F44336"
                                            Foreground="White"
                                            Padding="5,2"
                                            FontSize="10"
                                            BorderThickness="0"
                                            Margin="2"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- شريط الحالة -->
        <Border Grid.Row="3" Background="#E0E0E0" Padding="15,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"
                           FontSize="11"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding FilteredActivities.Count, StringFormat=عدد الأنشطة: {0}}" 
                               VerticalAlignment="Center"
                               FontSize="11"
                               Margin="10,0"/>
                    
                    <ProgressBar Width="100" 
                                 Height="15"
                                 IsIndeterminate="{Binding IsLoading}"
                                 Visibility="{Binding IsLoading, Converter={x:Static Converters:BoolToVisibilityConverter.Instance}}"
                                 Margin="10,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
