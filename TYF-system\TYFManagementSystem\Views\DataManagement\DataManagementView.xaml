<UserControl x:Class="TYFManagementSystem.Views.DataManagement.DataManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vm="clr-namespace:TYFManagementSystem.ViewModels.DataManagement"
             mc:Ignorable="d"
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.DataContext>
        <vm:DataManagementViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <StackPanel>
                <TextBlock Text="إدارة البيانات والمعلومات" 
                         FontSize="24" 
                         FontWeight="Bold" 
                         Foreground="#2E7D32"
                         HorizontalAlignment="Center"/>
                <TextBlock Text="إدارة شاملة لجميع بيانات النظام مع إمكانيات البحث والفلترة المتقدمة" 
                         FontSize="14" 
                         Foreground="#757575"
                         HorizontalAlignment="Center"
                         Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Search and Filter Section -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,15">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Search Controls -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Search TextBox -->
                    <TextBox Grid.Column="0" 
                             Text="{Binding SearchCriteria.SearchTerm, UpdateSourceTrigger=PropertyChanged}"
                             FontSize="14"
                             Padding="10"
                             Margin="0,0,10,0"
                             VerticalContentAlignment="Center">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                            <Border Background="White" 
                                                    BorderBrush="#E0E0E0" 
                                                    BorderThickness="1" 
                                                    CornerRadius="4">
                                                <Grid>
                                                    <TextBox x:Name="PART_EditableTextBox"
                                                             Background="Transparent"
                                                             BorderThickness="0"
                                                             Padding="10"
                                                             VerticalContentAlignment="Center"/>
                                                    <TextBlock Text="البحث في المشاريع..."
                                                               Foreground="#999"
                                                               Margin="15,0,0,0"
                                                               VerticalAlignment="Center"
                                                               IsHitTestVisible="False">
                                                        <TextBlock.Style>
                                                            <Style TargetType="TextBlock">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                                <Style.Triggers>
                                                                    <DataTrigger Binding="{Binding Text, ElementName=PART_EditableTextBox}" Value="">
                                                                        <Setter Property="Visibility" Value="Visible"/>
                                                                    </DataTrigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </TextBlock.Style>
                                                    </TextBlock>
                                                </Grid>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <!-- Status Filter -->
                    <ComboBox Grid.Column="1" 
                              ItemsSource="{Binding AvailableStatuses}"
                              SelectedItem="{Binding SearchCriteria.StatusFilter}"
                              FontSize="14"
                              Padding="10"
                              Margin="0,0,10,0"/>

                    <!-- Region Filter -->
                    <ComboBox Grid.Column="2" 
                              ItemsSource="{Binding AvailableRegions}"
                              SelectedItem="{Binding SearchCriteria.RegionFilter}"
                              FontSize="14"
                              Padding="10"
                              Margin="0,0,10,0"/>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="3" Orientation="Horizontal">
                        <Button Content="بحث"
                                Command="{Binding SearchCommand}"
                                Background="#2E7D32"
                                Foreground="White"
                                Padding="20,8"
                                Margin="5,0"
                                FontSize="14"
                                FontWeight="Bold"
                                BorderThickness="0"
                                CornerRadius="4"
                                Cursor="Hand"/>
                        
                        <Button Content="مسح"
                                Command="{Binding ClearSearchCommand}"
                                Background="#757575"
                                Foreground="White"
                                Padding="20,8"
                                Margin="5,0"
                                FontSize="14"
                                FontWeight="Bold"
                                BorderThickness="0"
                                CornerRadius="4"
                                Cursor="Hand"/>
                        
                        <Button Content="تحديث"
                                Command="{Binding RefreshCommand}"
                                Background="#FF9800"
                                Foreground="White"
                                Padding="20,8"
                                Margin="5,0"
                                FontSize="14"
                                FontWeight="Bold"
                                BorderThickness="0"
                                CornerRadius="4"
                                Cursor="Hand"/>
                    </StackPanel>
                </Grid>

                <!-- Status Message -->
                <TextBlock Grid.Row="1" 
                           Text="{Binding StatusMessage}"
                           FontSize="12"
                           Foreground="#666"
                           Margin="0,10,0,0"
                           HorizontalAlignment="Left"/>
            </Grid>
        </Border>

        <!-- Main Content with Tabs -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="0">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            
            <TabControl Background="Transparent" BorderThickness="0">
                <!-- Projects Tab -->
                <TabItem Header="المشاريع" FontSize="14" FontWeight="Bold">
                    <Grid Margin="20">
                        <DataGrid ItemsSource="{Binding Projects}"
                                  SelectedItem="{Binding SelectedProject}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  SelectionMode="Single"
                                  AlternatingRowBackground="#F5F5F5"
                                  RowBackground="White"
                                  FontSize="12">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="رقم المشروع" Binding="{Binding ProjectNumber}" Width="120"/>
                                <DataGridTextColumn Header="اسم المشروع" Binding="{Binding Name}" Width="*" MinWidth="200"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                <DataGridTextColumn Header="المنطقة" Binding="{Binding Region}" Width="100"/>
                                <DataGridTextColumn Header="المدير" Binding="{Binding Manager}" Width="120"/>
                                <DataGridTextColumn Header="الميزانية" Binding="{Binding BudgetFormatted}" Width="120"/>
                                <DataGridTextColumn Header="المستفيدين" Binding="{Binding BeneficiariesFormatted}" Width="100"/>
                                <DataGridTextColumn Header="المؤشرات" Binding="{Binding IndicatorsFormatted}" Width="80"/>
                                <DataGridTextColumn Header="التقدم" Binding="{Binding ProgressFormatted}" Width="80"/>
                                
                                <!-- Action Buttons Column -->
                                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="عرض التفاصيل"
                                                    Command="{Binding DataContext.ViewProjectDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    Background="#2196F3"
                                                    Foreground="White"
                                                    Padding="8,4"
                                                    FontSize="10"
                                                    BorderThickness="0"
                                                    CornerRadius="3"
                                                    Cursor="Hand"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </TabItem>

                <!-- Reports Tab -->
                <TabItem Header="التقارير" FontSize="14" FontWeight="Bold">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <DataGrid Grid.Row="0"
                                  ItemsSource="{Binding Reports}"
                                  SelectedItem="{Binding SelectedReport}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  SelectionMode="Single"
                                  AlternatingRowBackground="#F5F5F5"
                                  RowBackground="White"
                                  FontSize="12">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم التقرير" Binding="{Binding Name}" Width="*" MinWidth="200"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>
                                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=yyyy-MM-dd HH:mm}" Width="150"/>
                                <DataGridTextColumn Header="المنشئ" Binding="{Binding CreatedBy}" Width="120"/>
                                
                                <!-- Action Buttons Column -->
                                <DataGridTemplateColumn Header="الإجراءات" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="حذف"
                                                    Command="{Binding DataContext.DeleteReportCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    Background="#F44336"
                                                    Foreground="White"
                                                    Padding="8,4"
                                                    FontSize="10"
                                                    BorderThickness="0"
                                                    CornerRadius="3"
                                                    Cursor="Hand"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                            <Button Content="تصدير البيانات"
                                    Command="{Binding ExportDataCommand}"
                                    Background="#4CAF50"
                                    Foreground="White"
                                    Padding="15,8"
                                    FontSize="14"
                                    FontWeight="Bold"
                                    BorderThickness="0"
                                    CornerRadius="4"
                                    Cursor="Hand"/>
                        </StackPanel>
                    </Grid>
                </TabItem>

                <!-- Statistics Tab -->
                <TabItem Header="الإحصائيات" FontSize="14" FontWeight="Bold">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- General Statistics Cards -->
                            <Grid Grid.Row="0" Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Total Projects Card -->
                                <Border Grid.Column="0" Background="#2E7D32" CornerRadius="8" Padding="20" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="إجمالي المشاريع" Foreground="White" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding DatabaseStats.TotalProjects}" Foreground="White" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                    </StackPanel>
                                </Border>

                                <!-- Active Projects Card -->
                                <Border Grid.Column="1" Background="#4CAF50" CornerRadius="8" Padding="20" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="المشاريع النشطة" Foreground="White" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding DatabaseStats.ActiveProjects}" Foreground="White" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                    </StackPanel>
                                </Border>

                                <!-- Total Beneficiaries Card -->
                                <Border Grid.Column="2" Background="#FF9800" CornerRadius="8" Padding="20" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="إجمالي المستفيدين" Foreground="White" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding DatabaseStats.TotalBeneficiariesFormatted}" Foreground="White" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                    </StackPanel>
                                </Border>

                                <!-- Total Budget Card -->
                                <Border Grid.Column="3" Background="#2196F3" CornerRadius="8" Padding="20" Margin="5">
                                    <StackPanel>
                                        <TextBlock Text="إجمالي الميزانية" Foreground="White" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        <TextBlock Text="{Binding DatabaseStats.TotalBudgetFormatted}" Foreground="White" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- Projects by Region -->
                            <Border Grid.Row="1" Background="#F5F5F5" CornerRadius="8" Padding="20" Margin="0,0,0,20">
                                <StackPanel>
                                    <TextBlock Text="المشاريع حسب المنطقة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                                    <ItemsControl ItemsSource="{Binding DatabaseStats.ProjectsByRegion}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" CornerRadius="4" Padding="15" Margin="0,0,0,10">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        
                                                        <TextBlock Grid.Column="0" Text="{Binding Region}" FontWeight="Bold" VerticalAlignment="Center"/>
                                                        <TextBlock Grid.Column="1" Text="{Binding ProjectCount, StringFormat='{}{0} مشروع'}" Margin="10,0" VerticalAlignment="Center"/>
                                                        <TextBlock Grid.Column="2" Text="{Binding TotalBeneficiariesFormatted}" Margin="10,0" VerticalAlignment="Center"/>
                                                        <TextBlock Grid.Column="3" Text="{Binding TotalBudgetFormatted}" Margin="10,0" VerticalAlignment="Center"/>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Border>

                            <!-- Projects by Status -->
                            <Border Grid.Row="2" Background="#F5F5F5" CornerRadius="8" Padding="20">
                                <StackPanel>
                                    <TextBlock Text="المشاريع حسب الحالة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                                    <ItemsControl ItemsSource="{Binding DatabaseStats.ProjectsByStatus}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border Background="White" CornerRadius="4" Padding="15" Margin="0,0,0,10">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        
                                                        <TextBlock Grid.Column="0" Text="{Binding Status}" FontWeight="Bold" VerticalAlignment="Center"/>
                                                        <TextBlock Grid.Column="1" Text="{Binding Count, StringFormat='{}{0} مشروع'}" Margin="10,0" VerticalAlignment="Center"/>
                                                        <TextBlock Grid.Column="2" Text="{Binding PercentageFormatted}" Margin="10,0" VerticalAlignment="Center"/>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </ScrollViewer>
                </TabItem>
            </TabControl>
        </Border>

        <!-- Loading Indicator -->
        <Border Grid.Row="3" 
                Background="#FFF3E0" 
                CornerRadius="6" 
                Padding="15,10" 
                Margin="0,15,0,0"
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="⏳" FontSize="16" Margin="0,0,10,0"/>
                <TextBlock Text="جاري المعالجة..." FontSize="14" FontWeight="Bold" Foreground="#E65100"/>
            </StackPanel>
        </Border>
    </Grid>

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
</UserControl>
