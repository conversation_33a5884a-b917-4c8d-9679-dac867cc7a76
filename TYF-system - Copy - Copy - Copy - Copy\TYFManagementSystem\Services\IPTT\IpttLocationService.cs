using System.Collections.ObjectModel;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة إدارة المواقع في IPTT - مسؤولة عن إدارة البيانات متعددة المواقع
    /// </summary>
    public class IpttLocationService
    {
        private readonly IpttCalculationService _calculationService;

        public IpttLocationService()
        {
            _calculationService = new IpttCalculationService();
        }

        /// <summary>
        /// إنشاء بيانات المواقع بناءً على البيانات الأساسية
        /// </summary>
        public Dictionary<int, ObservableCollection<IpttDisplayRow>> CreateLocationData(
            ObservableCollection<IpttDisplayRow> baseDisplayRows,
            List<string> monthColumns,
            int locationCount)
        {
            var locationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>();

            try
            {
                if (baseDisplayRows == null || baseDisplayRows.Count == 0)
                {
                    throw new ArgumentException("لا توجد بيانات أساسية لنسخها إلى المواقع");
                }

                // إنشاء بيانات لكل موقع مع نسخ عميقة
                for (int i = 1; i <= locationCount; i++)
                {
                    locationData[i] = CreateLocationDisplayRows(baseDisplayRows, monthColumns);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء بيانات الموقع {i} مع {locationData[i].Count} مؤشر");
                }

                // إنشاء تبويب الإجمالي إذا كان هناك أكثر من موقع واحد
                if (locationCount > 1)
                {
                    locationData[0] = CreateTotalDisplayRows(baseDisplayRows, monthColumns);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء تبويب الإجمالي مع {locationData[0].Count} مؤشر");
                }

                System.Diagnostics.Debug.WriteLine($"🎉 تم إنشاء بيانات {locationCount} موقع بنجاح");
                return locationData;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء بيانات المواقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء صفوف عرض لموقع واحد
        /// </summary>
        private ObservableCollection<IpttDisplayRow> CreateLocationDisplayRows(
            ObservableCollection<IpttDisplayRow> baseRows,
            List<string> monthColumns)
        {
            var locationRows = new ObservableCollection<IpttDisplayRow>();

            foreach (var baseRow in baseRows)
            {
                var newRow = new IpttDisplayRow
                {
                    No = baseRow.No,
                    Indicator = baseRow.Indicator,
                    DataType = baseRow.DataType,
                    Target = baseRow.Target,
                    Achievement = "",
                    AchievementPercentage = "",
                    IsMainIndicator = baseRow.IsMainIndicator,
                    IsDataTypeRow = baseRow.IsDataTypeRow,
                    IsTotalRow = false,
                    IsCompleted = false,
                    CanDelete = baseRow.CanDelete,
                    IndicatorId = baseRow.IndicatorId,
                    MonthlyData = new Dictionary<string, string>()
                };

                // تهيئة البيانات الشهرية (فارغة للمواقع الجديدة)
                foreach (var month in monthColumns)
                {
                    newRow.MonthlyData[month] = "";
                }

                locationRows.Add(newRow);
            }

            return locationRows;
        }

        /// <summary>
        /// إنشاء صفوف عرض للإجمالي
        /// </summary>
        private ObservableCollection<IpttDisplayRow> CreateTotalDisplayRows(
            ObservableCollection<IpttDisplayRow> baseRows,
            List<string> monthColumns)
        {
            var totalRows = new ObservableCollection<IpttDisplayRow>();

            foreach (var baseRow in baseRows)
            {
                // نسخ نفس الهدف من الصف الأساسي (وليس الجمع)
                // مثال: إذا كان الهدف = 44، فالهدف في الإجمالي = 44 (وليس 44×عدد المواقع)
                string totalTarget = baseRow.Target; // نسخ نفس القيمة بالضبط

                var totalRow = new IpttDisplayRow
                {
                    No = baseRow.No,
                    Indicator = baseRow.Indicator,
                    DataType = baseRow.DataType,
                    Target = totalTarget, // نسخ الهدف الأساسي مؤقتاً
                    Achievement = "0", // سيتم حسابه تلقائياً
                    AchievementPercentage = "0%", // سيتم حسابه تلقائياً
                    IsMainIndicator = baseRow.IsMainIndicator,
                    IsDataTypeRow = baseRow.IsDataTypeRow,
                    IsTotalRow = true,
                    IsCompleted = false,
                    CanDelete = false, // لا يمكن حذف صفوف الإجمالي
                    IndicatorId = baseRow.IndicatorId,
                    MonthlyData = new Dictionary<string, string>()
                };

                // تهيئة البيانات الشهرية بالصفر
                foreach (var month in monthColumns)
                {
                    totalRow.MonthlyData[month] = "0";
                }

                totalRows.Add(totalRow);
            }

            return totalRows;
        }

        /// <summary>
        /// نسخ البيانات من موقع إلى آخر
        /// </summary>
        public void CopyLocationData(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData,
            int sourceLocationId,
            int targetLocationId)
        {
            try
            {
                if (!locationData.ContainsKey(sourceLocationId) ||
                    !locationData.ContainsKey(targetLocationId))
                {
                    throw new ArgumentException("معرف الموقع غير صحيح");
                }

                var sourceRows = locationData[sourceLocationId];
                var targetRows = locationData[targetLocationId];

                for (int i = 0; i < Math.Min(sourceRows.Count, targetRows.Count); i++)
                {
                    var sourceRow = sourceRows[i];
                    var targetRow = targetRows[i];

                    // نسخ البيانات الشهرية فقط
                    foreach (var monthData in sourceRow.MonthlyData)
                    {
                        targetRow.MonthlyData[monthData.Key] = monthData.Value;
                    }

                    // نسخ الأهداف إذا كانت فارغة في الهدف
                    if (string.IsNullOrWhiteSpace(targetRow.Target) && 
                        !string.IsNullOrWhiteSpace(sourceRow.Target))
                    {
                        targetRow.Target = sourceRow.Target;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في نسخ بيانات الموقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث جميع حسابات المواقع
        /// </summary>
        public void RefreshAllLocationCalculations(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData,
            List<string> monthColumns)
        {
            try
            {
                // حساب الإنجاز لجميع المواقع (باستثناء الإجمالي)
                foreach (var locationKvp in locationData.Where(l => l.Key > 0))
                {
                    _calculationService.CalculateAchievementForLocation(
                        locationKvp.Value, monthColumns);
                }

                // تحديث تبويب الإجمالي إذا كان موجوداً
                if (locationData.ContainsKey(0))
                {
                    _calculationService.CalculateTotalFromAllLocations(
                        locationData[0], locationData, monthColumns);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحديث حسابات المواقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// ربط البيانات المحملة بالمواقع
        /// </summary>
        public void ConnectLoadedDataToLocations(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> existingLocationData,
            Dictionary<int, ObservableCollection<IpttDisplayRow>> targetLocationData,
            List<string> monthColumns)
        {
            try
            {
                foreach (var existingLocationKvp in existingLocationData)
                {
                    int locationId = existingLocationKvp.Key;
                    var existingRows = existingLocationKvp.Value;

                    // إنشاء الموقع في البيانات المستهدفة إذا لم يكن موجوداً
                    if (!targetLocationData.ContainsKey(locationId))
                    {
                        targetLocationData[locationId] = new ObservableCollection<IpttDisplayRow>();
                    }

                    var targetRows = targetLocationData[locationId];

                    // ربط البيانات
                    ConnectRowsData(existingRows, targetRows, monthColumns);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في ربط البيانات المحملة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// ربط بيانات الصفوف
        /// </summary>
        private void ConnectRowsData(
            ObservableCollection<IpttDisplayRow> sourceRows,
            ObservableCollection<IpttDisplayRow> targetRows,
            List<string> monthColumns)
        {
            foreach (var sourceRow in sourceRows)
            {
                var targetRow = targetRows.FirstOrDefault(r =>
                    r.IndicatorId == sourceRow.IndicatorId &&
                    r.DataType == sourceRow.DataType &&
                    r.IsMainIndicator == sourceRow.IsMainIndicator &&
                    r.IsDataTypeRow == sourceRow.IsDataTypeRow);

                if (targetRow != null)
                {
                    // نسخ البيانات الشهرية
                    foreach (var monthData in sourceRow.MonthlyData)
                    {
                        if (targetRow.MonthlyData.ContainsKey(monthData.Key))
                        {
                            targetRow.MonthlyData[monthData.Key] = monthData.Value;
                        }
                    }

                    // نسخ البيانات الأخرى
                    targetRow.Target = sourceRow.Target;
                    targetRow.Achievement = sourceRow.Achievement;
                    targetRow.AchievementPercentage = sourceRow.AchievementPercentage;
                    targetRow.IsCompleted = sourceRow.IsCompleted;
                }
            }
        }

        /// <summary>
        /// التحقق من تطابق بيانات المواقع
        /// </summary>
        public bool ValidateLocationDataConsistency(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            try
            {
                if (!locationData.Any())
                    return false;

                // الحصول على الموقع الأول كمرجع
                var referenceLocation = locationData.Values.First();
                int expectedRowCount = referenceLocation.Count;

                // التحقق من أن جميع المواقع لها نفس عدد الصفوف
                foreach (var locationRows in locationData.Values)
                {
                    if (locationRows.Count != expectedRowCount)
                        return false;
                }

                // التحقق من تطابق هيكل البيانات
                foreach (var locationRows in locationData.Values)
                {
                    for (int i = 0; i < expectedRowCount; i++)
                    {
                        var referenceRow = referenceLocation[i];
                        var currentRow = locationRows[i];

                        if (referenceRow.IndicatorId != currentRow.IndicatorId ||
                            referenceRow.DataType != currentRow.DataType ||
                            referenceRow.IsMainIndicator != currentRow.IsMainIndicator ||
                            referenceRow.IsDataTypeRow != currentRow.IsDataTypeRow)
                        {
                            return false;
                        }
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على أسماء المواقع
        /// </summary>
        public Dictionary<int, string> GetLocationNames(int locationCount)
        {
            var locationNames = new Dictionary<int, string>();

            for (int i = 1; i <= locationCount; i++)
            {
                locationNames[i] = $"الموقع {i}";
            }

            if (locationCount > 1)
            {
                locationNames[0] = "الإجمالي";
            }

            return locationNames;
        }

        /// <summary>
        /// تحديد عدد المواقع من البيانات المحملة
        /// </summary>
        public int DetermineLocationCountFromData(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            if (!locationData.Any())
                return 1;

            // العثور على أعلى رقم موقع (باستثناء 0 وهو الإجمالي)
            var maxLocationId = locationData.Keys.Where(k => k > 0).DefaultIfEmpty(1).Max();
            return maxLocationId;
        }

        /// <summary>
        /// إزالة موقع من البيانات
        /// </summary>
        public void RemoveLocation(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData,
            int locationId)
        {
            try
            {
                if (locationData.ContainsKey(locationId))
                {
                    locationData.Remove(locationId);
                }

                // إعادة ترقيم المواقع إذا لزم الأمر
                ReorderLocationIds(locationData);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إزالة الموقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إعادة ترقيم معرفات المواقع
        /// </summary>
        private void ReorderLocationIds(Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            var sortedLocations = locationData.Where(l => l.Key > 0)
                                            .OrderBy(l => l.Key)
                                            .ToList();

            // إزالة المواقع القديمة
            foreach (var location in sortedLocations)
            {
                locationData.Remove(location.Key);
            }

            // إعادة إضافة المواقع بترقيم جديد
            for (int i = 0; i < sortedLocations.Count; i++)
            {
                locationData[i + 1] = sortedLocations[i].Value;
            }
        }
    }
}
