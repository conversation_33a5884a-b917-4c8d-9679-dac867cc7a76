using System;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Models.Reports;
using TYFManagementSystem.Services.Reports;

namespace TYFManagementSystem.Views.Reports
{
    /// <summary>
    /// Interaction logic for ExportReportDialog.xaml
    /// </summary>
    public partial class ExportReportDialog : Window
    {
        private readonly ReportGenerationResult _reportResult;
        private readonly ReportExportService _exportService;
        private string? _lastExportedFilePath;

        public ExportReportDialog(ReportGenerationResult reportResult)
        {
            InitializeComponent();
            _reportResult = reportResult;
            _exportService = new ReportExportService();
            
            LoadReportInfo();
            SetupEventHandlers();
            UpdateFileNamePreview();
        }

        private void LoadReportInfo()
        {
            if (_reportResult?.Report != null)
            {
                ReportNameTextBlock.Text = $"تصدير: {_reportResult.Report.Name}";
                ReportTitleTextBlock.Text = _reportResult.Report.Name;
                RecordCountTextBlock.Text = _reportResult.RecordCount.ToString();
                
                // تقدير حجم الملف
                var estimatedSize = EstimateFileSize();
                EstimatedSizeTextBlock.Text = estimatedSize;
            }
        }

        private void SetupEventHandlers()
        {
            // تحديث معاينة اسم الملف عند تغيير الصيغة
            ExcelRadioButton.Checked += (s, e) => UpdateFileNamePreview();
            CsvRadioButton.Checked += (s, e) => UpdateFileNamePreview();
            JsonRadioButton.Checked += (s, e) => UpdateFileNamePreview();
            XmlRadioButton.Checked += (s, e) => UpdateFileNamePreview();
            PdfRadioButton.Checked += (s, e) => UpdateFileNamePreview();
        }

        private void UpdateFileNamePreview()
        {
            if (_reportResult?.Report == null) return;

            var extension = GetSelectedFormat() switch
            {
                ExportFormat.Excel => ".xlsx",
                ExportFormat.CSV => ".csv",
                ExportFormat.JSON => ".json",
                ExportFormat.XML => ".xml",
                ExportFormat.PDF => ".html",
                _ => ".txt"
            };

            var fileName = $"{_reportResult.Report.Name}_{DateTime.Now:yyyyMMdd_HHmm}{extension}";
            FileNamePreviewTextBox.Text = fileName;
        }

        private ExportFormat GetSelectedFormat()
        {
            if (ExcelRadioButton.IsChecked == true) return ExportFormat.Excel;
            if (CsvRadioButton.IsChecked == true) return ExportFormat.CSV;
            if (JsonRadioButton.IsChecked == true) return ExportFormat.JSON;
            if (XmlRadioButton.IsChecked == true) return ExportFormat.XML;
            if (PdfRadioButton.IsChecked == true) return ExportFormat.PDF;
            return ExportFormat.Excel;
        }

        private string EstimateFileSize()
        {
            try
            {
                var recordCount = _reportResult.RecordCount;
                var format = GetSelectedFormat();

                var estimatedBytes = format switch
                {
                    ExportFormat.Excel => recordCount * 150, // تقدير 150 بايت لكل سجل في Excel
                    ExportFormat.CSV => recordCount * 100,   // تقدير 100 بايت لكل سجل في CSV
                    ExportFormat.JSON => recordCount * 200,  // تقدير 200 بايت لكل سجل في JSON
                    ExportFormat.XML => recordCount * 250,   // تقدير 250 بايت لكل سجل في XML
                    ExportFormat.PDF => recordCount * 300,   // تقدير 300 بايت لكل سجل في HTML
                    _ => recordCount * 100
                };

                return estimatedBytes switch
                {
                    < 1024 => $"{estimatedBytes} بايت",
                    < 1024 * 1024 => $"{estimatedBytes / 1024:F1} كيلوبايت",
                    _ => $"{estimatedBytes / (1024 * 1024):F1} ميجابايت"
                };
            }
            catch
            {
                return "غير محدد";
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ExportButton.IsEnabled = false;
                var originalContent = ExportButton.Content;
                ExportButton.Content = "⏳ جاري التصدير...";

                var format = GetSelectedFormat();
                var success = await _exportService.ExportReportAsync(_reportResult, format);

                if (success)
                {
                    MessageBox.Show("تم تصدير التقرير بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // فتح الملف إذا كان المستخدم يريد ذلك
                    if (OpenAfterExportCheckBox.IsChecked == true && !string.IsNullOrEmpty(_lastExportedFilePath))
                    {
                        try
                        {
                            Process.Start(new ProcessStartInfo
                            {
                                FileName = _lastExportedFilePath,
                                UseShellExecute = true
                            });
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"تم التصدير بنجاح ولكن فشل في فتح الملف: {ex.Message}", 
                                "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }

                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في تصدير التقرير", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }

                ExportButton.IsEnabled = true;
                ExportButton.Content = originalContent;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                ExportButton.IsEnabled = true;
                ExportButton.Content = "📤 تصدير الآن";
            }
        }

        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var previewDialog = new ReportPreviewDialog(_reportResult);
                previewDialog.Owner = this;
                previewDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المعاينة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            // تنظيف الموارد إذا لزم الأمر
            base.OnClosed(e);
        }
    }
}
