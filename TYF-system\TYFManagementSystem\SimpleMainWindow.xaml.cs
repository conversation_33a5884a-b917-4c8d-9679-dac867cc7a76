using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Views.Projects;
using TYFManagementSystem.Views.Reports;
using TYFManagementSystem.Views.ME;
using TYFManagementSystem.Views.DataManagement;
using TYFManagementSystem.ViewModels.Dashboard;
using TYFManagementSystem.Views.Activities;

namespace TYFManagementSystem;

/// <summary>
/// Interaction logic for SimpleMainWindow.xaml
/// </summary>
public partial class SimpleMainWindow : Window
{
    private DashboardViewModel? _dashboardViewModel;

    public SimpleMainWindow()
    {
        InitializeComponent();
        InitializeDashboard();
    }

    private void InitializeDashboard()
    {
        _dashboardViewModel = new DashboardViewModel();
        _dashboardViewModel.NavigationRequested += OnNavigationRequested;

        // Set the dashboard as the default content
        ShowDashboard();
    }

    private void OnNavigationRequested(string targetView)
    {
        switch (targetView)
        {
            case "المشاريع":
                ProjectsButton_Click(this, new RoutedEventArgs());
                break;
            case "التقارير":
                ReportsButton_Click(this, new RoutedEventArgs());
                break;
            case "المتابعة والتقييم":
                MEButton_Click(this, new RoutedEventArgs());
                break;
            case "إدارة البيانات":
                DataManagementButton_Click(this, new RoutedEventArgs());
                break;
        }
    }

    private void ShowDashboard()
    {
        // Create dashboard content with dynamic data
        var dashboardContent = CreateDashboardContent();
        MainContentArea.Content = dashboardContent;
    }

    private void ProjectsButton_Click(object sender, RoutedEventArgs e)
    {
        // Replace main content with Projects view
        var projectsView = new ProjectsView();
        MainContentArea.Content = projectsView;
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        // Replace main content with Reports Management view
        var reportsView = new ReportsManagementView();
        MainContentArea.Content = reportsView;
    }

    private void MEButton_Click(object sender, RoutedEventArgs e)
    {
        // Replace main content with M&E Dashboard view
        var meView = new MEDashboardView();
        MainContentArea.Content = meView;
    }

    private void ActivitiesButton_Click(object sender, RoutedEventArgs e)
    {
        // Replace main content with Activities Management view
        var activitiesView = new ActivitiesManagementView();
        MainContentArea.Content = activitiesView;
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        // Return to dashboard with dynamic content
        ShowDashboard();
    }

    private void DataManagementButton_Click(object sender, RoutedEventArgs e)
    {
        // Replace main content with Data Management view
        var dataManagementView = new DataManagementView();
        MainContentArea.Content = dataManagementView;
    }

    private ScrollViewer CreateDashboardContent()
    {
        var scrollViewer = new ScrollViewer
        {
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Disabled
        };

        var border = new Border
        {
            Background = System.Windows.Media.Brushes.White,
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(40),
            Margin = new Thickness(0, 0, 0, 20)
        };

        border.Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = System.Windows.Media.Colors.Gray,
            Direction = 270,
            ShadowDepth = 3,
            Opacity = 0.25,
            BlurRadius = 12
        };

        var stackPanel = new StackPanel();

        // Header
        var headerText = new TextBlock
        {
            Text = "مرحباً بك في نظام إدارة مؤسسة تمدين شباب",
            FontSize = 28,
            FontWeight = FontWeights.Bold,
            Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(46, 125, 50)),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 15),
            TextWrapping = TextWrapping.Wrap
        };

        var subHeaderText = new TextBlock
        {
            Text = "نظام إدارة متكامل للمؤسسات الإنسانية",
            FontSize = 18,
            Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(117, 117, 117)),
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 50),
            TextWrapping = TextWrapping.Wrap
        };

        stackPanel.Children.Add(headerText);
        stackPanel.Children.Add(subHeaderText);

        // Statistics Cards Grid
        var cardsGrid = CreateStatisticsCardsGrid();
        stackPanel.Children.Add(cardsGrid);

        border.Child = stackPanel;
        scrollViewer.Content = border;

        // Set DataContext for binding
        if (_dashboardViewModel != null)
        {
            scrollViewer.DataContext = _dashboardViewModel;
        }

        return scrollViewer;
    }

    private Grid CreateStatisticsCardsGrid()
    {
        var grid = new Grid();

        // Define columns
        for (int i = 0; i < 4; i++)
        {
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star), MinWidth = 200 });
        }

        // Create cards with click handlers
        var cards = new[]
        {
            new { Title = "المشاريع النشطة", Value = "12", Color = "#2E7D32", Target = "المشاريع" },
            new { Title = "المستفيدين", Value = "1,245", Color = "#4CAF50", Target = "المشاريع" },
            new { Title = "التقارير الشهرية", Value = "8", Color = "#81C784", Target = "التقارير" },
            new { Title = "المؤشرات النشطة", Value = "24", Color = "#FF9800", Target = "المتابعة والتقييم" }
        };

        for (int i = 0; i < cards.Length; i++)
        {
            var card = cards[i];
            var cardBorder = CreateStatisticsCard(card.Title, card.Value, card.Color, card.Target);
            Grid.SetColumn(cardBorder, i);
            grid.Children.Add(cardBorder);
        }

        return grid;
    }

    private Border CreateStatisticsCard(string title, string value, string colorHex, string target)
    {
        var border = new Border
        {
            Background = new System.Windows.Media.SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(colorHex)),
            CornerRadius = new CornerRadius(12),
            Padding = new Thickness(25),
            Margin = new Thickness(10),
            Cursor = System.Windows.Input.Cursors.Hand
        };

        border.Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = System.Windows.Media.Colors.Black,
            Direction = 270,
            ShadowDepth = 2,
            Opacity = 0.3,
            BlurRadius = 8
        };

        // Add click handler
        border.MouseLeftButtonUp += (s, e) => OnNavigationRequested(target);

        var stackPanel = new StackPanel();

        var titleText = new TextBlock
        {
            Text = title,
            Foreground = System.Windows.Media.Brushes.White,
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            TextWrapping = TextWrapping.Wrap
        };

        var valueText = new TextBlock
        {
            Text = value,
            Foreground = System.Windows.Media.Brushes.White,
            FontSize = 36,
            FontWeight = FontWeights.Bold,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 15, 0, 0)
        };

        stackPanel.Children.Add(titleText);
        stackPanel.Children.Add(valueText);
        border.Child = stackPanel;

        return border;
    }
}
