using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.Activities;
using TYFManagementSystem.Services.Activities;

namespace TYFManagementSystem.ViewModels.Activities
{
    /// <summary>
    /// ViewModel لإدارة الأنشطة الميدانية
    /// </summary>
    public class ActivitiesManagementViewModel : BaseViewModel, IDisposable
    {
        #region Private Fields
        private readonly ActivityService _activityService;
        private ObservableCollection<ActivitySummary> _activities;
        private ObservableCollection<ActivitySummary> _filteredActivities;
        private ActivitySummary? _selectedActivity;
        private ActivitySearchFilters _searchFilters;
        private bool _isLoading;
        private string _statusMessage = "";
        private string _searchText = "";
        private string _selectedProjectFilter = "الكل";
        private string _selectedStatusFilter = "الكل";
        private string _selectedPriorityFilter = "الكل";
        private DateTime? _startDateFilter;
        private DateTime? _endDateFilter;
        #endregion

        #region Properties
        public ObservableCollection<ActivitySummary> Activities
        {
            get => _activities;
            set => SetProperty(ref _activities, value);
        }

        public ObservableCollection<ActivitySummary> FilteredActivities
        {
            get => _filteredActivities;
            set => SetProperty(ref _filteredActivities, value);
        }

        public ActivitySummary? SelectedActivity
        {
            get => _selectedActivity;
            set => SetProperty(ref _selectedActivity, value);
        }

        public ActivitySearchFilters SearchFilters
        {
            get => _searchFilters;
            set => SetProperty(ref _searchFilters, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    SearchFilters.SearchText = value;
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public string SelectedProjectFilter
        {
            get => _selectedProjectFilter;
            set
            {
                if (SetProperty(ref _selectedProjectFilter, value))
                {
                    SearchFilters.ProjectId = value == "الكل" ? null : value;
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public string SelectedStatusFilter
        {
            get => _selectedStatusFilter;
            set
            {
                if (SetProperty(ref _selectedStatusFilter, value))
                {
                    SearchFilters.Status = value == "الكل" ? null : ParseActivityStatus(value);
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public string SelectedPriorityFilter
        {
            get => _selectedPriorityFilter;
            set
            {
                if (SetProperty(ref _selectedPriorityFilter, value))
                {
                    SearchFilters.Priority = value == "الكل" ? null : ParseActivityPriority(value);
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public DateTime? StartDateFilter
        {
            get => _startDateFilter;
            set
            {
                if (SetProperty(ref _startDateFilter, value))
                {
                    SearchFilters.StartDateFrom = value;
                    _ = ApplyFiltersAsync();
                }
            }
        }

        public DateTime? EndDateFilter
        {
            get => _endDateFilter;
            set
            {
                if (SetProperty(ref _endDateFilter, value))
                {
                    SearchFilters.StartDateTo = value;
                    _ = ApplyFiltersAsync();
                }
            }
        }

        // خيارات الفلاتر
        public string[] ProjectFilterOptions { get; } = { "الكل", "مشروع 1", "مشروع 2", "مشروع 3" };
        public string[] StatusFilterOptions { get; } = { "الكل", "مخطط", "جاري التنفيذ", "مكتمل", "ملغي", "مؤجل", "متوقف مؤقتاً" };
        public string[] PriorityFilterOptions { get; } = { "الكل", "منخفضة", "متوسطة", "عالية", "حرجة" };
        #endregion

        #region Commands
        public ICommand LoadActivitiesCommand { get; private set; }
        public ICommand CreateNewActivityCommand { get; private set; }
        public ICommand EditActivityCommand { get; private set; }
        public ICommand DeleteActivityCommand { get; private set; }
        public ICommand ViewActivityDetailsCommand { get; private set; }
        public ICommand ShowOnMapCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand ClearFiltersCommand { get; private set; }
        public ICommand ExportActivitiesCommand { get; private set; }
        public ICommand ShowCalendarViewCommand { get; private set; }
        #endregion

        #region Constructor
        public ActivitiesManagementViewModel()
        {
            _activityService = new ActivityService();
            _activities = new ObservableCollection<ActivitySummary>();
            _filteredActivities = new ObservableCollection<ActivitySummary>();
            _searchFilters = new ActivitySearchFilters();

            InitializeCommands();
            _ = LoadActivitiesAsync();
        }
        #endregion

        #region Private Methods
        private void InitializeCommands()
        {
            LoadActivitiesCommand = new RelayCommand(async () => await LoadActivitiesAsync());
            CreateNewActivityCommand = new RelayCommand(async () => await CreateNewActivityAsync());
            EditActivityCommand = new RelayCommand(async () => await EditActivityAsync(), () => SelectedActivity != null);
            DeleteActivityCommand = new RelayCommand(async () => await DeleteActivityAsync(), () => SelectedActivity != null);
            ViewActivityDetailsCommand = new RelayCommand(async () => await ViewActivityDetailsAsync(), () => SelectedActivity != null);
            ShowOnMapCommand = new RelayCommand(async () => await ShowOnMapAsync(), () => SelectedActivity != null);
            RefreshCommand = new RelayCommand(async () => await RefreshAsync());
            ClearFiltersCommand = new RelayCommand(async () => await ClearFiltersAsync());
            ExportActivitiesCommand = new RelayCommand(async () => await ExportActivitiesAsync());
            ShowCalendarViewCommand = new RelayCommand(async () => await ShowCalendarViewAsync());
        }

        private async Task LoadActivitiesAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل الأنشطة...";

                var activities = await _activityService.GetActivitiesAsync(SearchFilters);

                Activities.Clear();
                FilteredActivities.Clear();

                foreach (var activity in activities)
                {
                    Activities.Add(activity);
                    FilteredActivities.Add(activity);
                }

                StatusMessage = $"تم تحميل {activities.Count} نشاط";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل الأنشطة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ApplyFiltersAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تطبيق الفلاتر...";

                var filteredActivities = await _activityService.GetActivitiesAsync(SearchFilters);

                FilteredActivities.Clear();
                foreach (var activity in filteredActivities)
                {
                    FilteredActivities.Add(activity);
                }

                StatusMessage = $"تم العثور على {filteredActivities.Count} نشاط";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تطبيق الفلاتر: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task CreateNewActivityAsync()
        {
            try
            {
                StatusMessage = "فتح نافذة إنشاء نشاط جديد...";
                // سيتم تنفيذ هذا لاحقاً - فتح نافذة إنشاء النشاط
                await Task.Delay(100);
                StatusMessage = "جاهز لإنشاء نشاط جديد";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في إنشاء النشاط: {ex.Message}";
            }
        }

        private async Task EditActivityAsync()
        {
            if (SelectedActivity == null) return;

            try
            {
                StatusMessage = $"تعديل النشاط: {SelectedActivity.ActivityName}";
                // سيتم تنفيذ هذا لاحقاً - فتح نافذة تعديل النشاط
                await Task.Delay(100);
                StatusMessage = "جاهز لتعديل النشاط";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تعديل النشاط: {ex.Message}";
            }
        }

        private async Task DeleteActivityAsync()
        {
            if (SelectedActivity == null) return;

            try
            {
                StatusMessage = $"حذف النشاط: {SelectedActivity.ActivityName}";
                
                var success = await _activityService.DeleteActivityAsync(SelectedActivity.ActivityId);
                if (success)
                {
                    Activities.Remove(SelectedActivity);
                    FilteredActivities.Remove(SelectedActivity);
                    StatusMessage = "تم حذف النشاط بنجاح";
                    SelectedActivity = null;
                }
                else
                {
                    StatusMessage = "فشل في حذف النشاط";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في حذف النشاط: {ex.Message}";
            }
        }

        private async Task ViewActivityDetailsAsync()
        {
            if (SelectedActivity == null) return;

            try
            {
                StatusMessage = $"عرض تفاصيل النشاط: {SelectedActivity.ActivityName}";
                // سيتم تنفيذ هذا لاحقاً - فتح نافذة تفاصيل النشاط
                await Task.Delay(100);
                StatusMessage = "جاهز لعرض التفاصيل";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في عرض التفاصيل: {ex.Message}";
            }
        }

        private async Task ShowOnMapAsync()
        {
            if (SelectedActivity == null) return;

            try
            {
                StatusMessage = $"عرض النشاط على الخريطة: {SelectedActivity.ActivityName}";
                // سيتم تنفيذ هذا لاحقاً - فتح الخريطة
                await Task.Delay(100);
                StatusMessage = "جاهز لعرض الخريطة";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في عرض الخريطة: {ex.Message}";
            }
        }

        private async Task RefreshAsync()
        {
            await LoadActivitiesAsync();
        }

        private async Task ClearFiltersAsync()
        {
            SearchText = "";
            SelectedProjectFilter = "الكل";
            SelectedStatusFilter = "الكل";
            SelectedPriorityFilter = "الكل";
            StartDateFilter = null;
            EndDateFilter = null;
            
            SearchFilters = new ActivitySearchFilters();
            await LoadActivitiesAsync();
        }

        private async Task ExportActivitiesAsync()
        {
            try
            {
                StatusMessage = "تصدير الأنشطة...";
                // سيتم تنفيذ هذا لاحقاً - تصدير البيانات
                await Task.Delay(100);
                StatusMessage = "جاهز للتصدير";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التصدير: {ex.Message}";
            }
        }

        private async Task ShowCalendarViewAsync()
        {
            try
            {
                StatusMessage = "فتح عرض التقويم...";

                // إنشاء نافذة التقويم
                var calendarWindow = new System.Windows.Window
                {
                    Title = "تقويم الأنشطة الميدانية",
                    Width = 1200,
                    Height = 800,
                    WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen,
                    Content = new TYFManagementSystem.Views.Activities.ActivityCalendarView()
                };

                calendarWindow.Show();
                StatusMessage = "تم فتح التقويم";

                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في عرض التقويم: {ex.Message}";
            }
        }

        private ActivityStatus? ParseActivityStatus(string statusText)
        {
            return statusText switch
            {
                "مخطط" => ActivityStatus.Planned,
                "جاري التنفيذ" => ActivityStatus.InProgress,
                "مكتمل" => ActivityStatus.Completed,
                "ملغي" => ActivityStatus.Cancelled,
                "مؤجل" => ActivityStatus.Postponed,
                "متوقف مؤقتاً" => ActivityStatus.OnHold,
                _ => null
            };
        }

        private ActivityPriority? ParseActivityPriority(string priorityText)
        {
            return priorityText switch
            {
                "منخفضة" => ActivityPriority.Low,
                "متوسطة" => ActivityPriority.Medium,
                "عالية" => ActivityPriority.High,
                "حرجة" => ActivityPriority.Critical,
                _ => null
            };
        }
        #endregion

        #region IDisposable
        public void Dispose()
        {
            _activityService?.Dispose();
        }
        #endregion
    }
}
