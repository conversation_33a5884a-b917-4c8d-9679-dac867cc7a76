using System.IO;
using TYFManagementSystem.Data;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة إعادة تعيين قاعدة البيانات
    /// </summary>
    public static class DatabaseResetService
    {
        /// <summary>
        /// حذف قاعدة البيانات القديمة وإعادة إنشائها
        /// </summary>
        public static async Task ResetDatabaseAsync()
        {
            try
            {
                // الحصول على مسار قاعدة البيانات
                var dbPath = GetDatabasePath();
                
                // حذف ملف قاعدة البيانات إذا كان موجوداً
                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                }
                
                // إعادة إنشاء قاعدة البيانات
                await DatabaseInitializer.InitializeDatabaseAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إعادة تعيين قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        private static string GetDatabasePath()
        {
            return Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "TYFManagementSystem",
                "TYF_Database.db"
            );
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات
        /// </summary>
        public static bool DatabaseExists()
        {
            var dbPath = GetDatabasePath();
            return File.Exists(dbPath);
        }

        /// <summary>
        /// الحصول على حجم قاعدة البيانات
        /// </summary>
        public static long GetDatabaseSize()
        {
            var dbPath = GetDatabasePath();
            if (File.Exists(dbPath))
            {
                var fileInfo = new FileInfo(dbPath);
                return fileInfo.Length;
            }
            return 0;
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        public static async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var dbPath = GetDatabasePath();
                if (File.Exists(dbPath))
                {
                    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                    var backupDir = Path.GetDirectoryName(backupPath);
                    if (!Directory.Exists(backupDir))
                    {
                        Directory.CreateDirectory(backupDir!);
                    }

                    // نسخ ملف قاعدة البيانات
                    File.Copy(dbPath, backupPath, true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// </summary>
        public static async Task<bool> RestoreDatabaseAsync(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    var dbPath = GetDatabasePath();
                    
                    // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
                    var dbDir = Path.GetDirectoryName(dbPath);
                    if (!Directory.Exists(dbDir))
                    {
                        Directory.CreateDirectory(dbDir!);
                    }

                    // نسخ النسخة الاحتياطية إلى مكان قاعدة البيانات
                    File.Copy(backupPath, dbPath, true);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
