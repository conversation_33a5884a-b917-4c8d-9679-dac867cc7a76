using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة حفظ وتحميل بيانات IPTT محلياً باستخدام ملفات JSON
    /// </summary>
    public class IpttLocalStorageService
    {
        private readonly string _dataDirectory;
        private readonly string _backupDirectory;

        public IpttLocalStorageService()
        {
            // إنشاء مجلد البيانات في مجلد التطبيق
            _dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "IPTT");
            _backupDirectory = Path.Combine(_dataDirectory, "Backups");
            
            // التأكد من وجود المجلدات
            Directory.CreateDirectory(_dataDirectory);
            Directory.CreateDirectory(_backupDirectory);
        }

        /// <summary>
        /// حفظ بيانات IPTT لمشروع معين
        /// </summary>
        public async Task<bool> SaveIpttDataAsync(string projectId, IpttLocalData data)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"💾 بدء حفظ بيانات IPTT للمشروع: {projectId}");

                var fileName = $"IPTT_Project_{projectId}.json";
                var filePath = Path.Combine(_dataDirectory, fileName);

                // إنشاء نسخة احتياطية إذا كان الملف موجوداً
                if (File.Exists(filePath))
                {
                    await CreateBackupAsync(filePath, projectId);
                }

                // تحويل البيانات إلى JSON
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var jsonData = JsonSerializer.Serialize(data, options);

                // كتابة البيانات إلى الملف
                await File.WriteAllTextAsync(filePath, jsonData);

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ البيانات بنجاح في: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل بيانات IPTT لمشروع معين
        /// </summary>
        public async Task<IpttLocalData?> LoadIpttDataAsync(string projectId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📂 بدء تحميل بيانات IPTT للمشروع: {projectId}");

                var fileName = $"IPTT_Project_{projectId}.json";
                var filePath = Path.Combine(_dataDirectory, fileName);

                if (!File.Exists(filePath))
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا يوجد ملف بيانات للمشروع: {projectId}");
                    return null;
                }

                // قراءة البيانات من الملف
                var jsonData = await File.ReadAllTextAsync(filePath);

                if (string.IsNullOrWhiteSpace(jsonData))
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ ملف البيانات فارغ للمشروع: {projectId}");
                    return null;
                }

                // تحويل JSON إلى كائن
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var data = JsonSerializer.Deserialize<IpttLocalData>(jsonData, options);

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل البيانات بنجاح من: {filePath}");
                System.Diagnostics.Debug.WriteLine($"📊 البيانات المحملة: {data?.Indicators?.Count ?? 0} مؤشر، {data?.LocationData?.Count ?? 0} موقع");

                return data;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من الملف
        /// </summary>
        private async Task CreateBackupAsync(string originalFilePath, string projectId)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"IPTT_Project_{projectId}_Backup_{timestamp}.json";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                File.Copy(originalFilePath, backupFilePath, true);
                System.Diagnostics.Debug.WriteLine($"📋 تم إنشاء نسخة احتياطية: {backupFileName}");

                // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 5 نسخ فقط)
                await CleanupOldBackupsAsync(projectId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف النسخ الاحتياطية القديمة
        /// </summary>
        private async Task CleanupOldBackupsAsync(string projectId)
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupDirectory, $"IPTT_Project_{projectId}_Backup_*.json")
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .Skip(5) // الاحتفاظ بآخر 5 نسخ
                    .ToList();

                foreach (var oldBackup in backupFiles)
                {
                    File.Delete(oldBackup);
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف النسخة الاحتياطية القديمة: {Path.GetFileName(oldBackup)}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تنظيف النسخ الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود بيانات لمشروع معين
        /// </summary>
        public bool HasDataForProject(string projectId)
        {
            var fileName = $"IPTT_Project_{projectId}.json";
            var filePath = Path.Combine(_dataDirectory, fileName);
            return File.Exists(filePath);
        }

        /// <summary>
        /// حذف بيانات مشروع معين
        /// </summary>
        public async Task<bool> DeleteProjectDataAsync(string projectId)
        {
            try
            {
                var fileName = $"IPTT_Project_{projectId}.json";
                var filePath = Path.Combine(_dataDirectory, fileName);

                if (File.Exists(filePath))
                {
                    // إنشاء نسخة احتياطية قبل الحذف
                    await CreateBackupAsync(filePath, projectId);
                    
                    File.Delete(filePath);
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف بيانات المشروع: {projectId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف بيانات المشروع: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة المشاريع التي لها بيانات محفوظة
        /// </summary>
        public List<string> GetProjectsWithData()
        {
            try
            {
                var files = Directory.GetFiles(_dataDirectory, "IPTT_Project_*.json");
                var projectIds = files
                    .Select(f => Path.GetFileNameWithoutExtension(f))
                    .Where(name => name.StartsWith("IPTT_Project_"))
                    .Select(name => name.Substring("IPTT_Project_".Length))
                    .ToList();

                return projectIds;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على قائمة المشاريع: {ex.Message}");
                return new List<string>();
            }
        }
    }

    /// <summary>
    /// نموذج البيانات المحلية لـ IPTT
    /// </summary>
    public class IpttLocalData
    {
        public List<IpttIndicator> Indicators { get; set; } = new List<IpttIndicator>();
        public List<string> MonthColumns { get; set; } = new List<string>();
        public Dictionary<int, List<IpttDisplayRow>> LocationData { get; set; } = new Dictionary<int, List<IpttDisplayRow>>();
        public int LocationCount { get; set; } = 1;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string ProjectId { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
    }
}
