@echo off
chcp 65001 >nul
title اختبار نقل النظام

echo ========================================
echo    اختبار نقل نظام إدارة مؤسسة تمدين شباب
echo ========================================
echo.

echo جاري فحص ملفات النظام...
echo.

REM فحص وجود الملف التنفيذي
if exist "TYFManagementSystem\bin\Release\net8.0-windows\TYFManagementSystem.exe" (
    echo ✅ الملف التنفيذي موجود
) else (
    echo ❌ الملف التنفيذي غير موجود
    goto :error
)

REM فحص وجود مجلد قاعدة البيانات
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Data" (
    echo ✅ مجلد قاعدة البيانات موجود
) else (
    echo ⚠️  مجلد قاعدة البيانات غير موجود - سيتم إنشاؤه تلقائياً
)

REM فحص وجود قاعدة البيانات
if exist "TYFManagementSystem\bin\Release\net8.0-windows\Data\TYF_Database.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ⚠️  قاعدة البيانات غير موجودة - سيتم إنشاؤها تلقائياً
)

REM فحص وجود ملف التشغيل السريع
if exist "تشغيل_النظام.bat" (
    echo ✅ ملف التشغيل السريع موجود
) else (
    echo ❌ ملف التشغيل السريع غير موجود
)

echo.
echo ========================================
echo النتيجة: النظام جاهز للنقل والتشغيل! 🎉
echo ========================================
echo.
echo لتشغيل النظام:
echo 1. انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
echo 2. أو شغل الملف التنفيذي مباشرة
echo.
echo لنقل النظام:
echo 1. انسخ مجلد "TYF-system" بالكامل
echo 2. ضعه في أي مكان في الجهاز الجديد
echo 3. شغل النظام - كل شيء سيعمل تلقائياً!
echo.
pause
exit /b 0

:error
echo.
echo ========================================
echo خطأ: ملفات النظام غير مكتملة
echo ========================================
echo تأكد من وجود جميع الملفات المطلوبة
echo.
pause
exit /b 1
